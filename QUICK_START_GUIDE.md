# 知识图谱系统快速启动指南

## 🚀 一键启动（推荐）

### 完整系统启动
```bash
start_full_system.bat
```

**这个脚本会**:
1. ✅ 检查 Python 和 Node.js 环境
2. ✅ 自动安装所需依赖
3. ✅ 启动前端服务（后台）
4. ✅ 启动后端服务（前台）
5. ✅ 自动打开前端网页

**启动后访问**:
- 🌐 **前端应用**: http://localhost:3000
- 🔗 **后端API**: http://localhost:8000
- 📚 **API文档**: http://localhost:8000/docs

## 🔧 分别启动

### 方式1: 先启动后端
```bash
start_backend.bat
```
- 启动后端服务
- **自动打开前端页面** (http://localhost:3000)
- 检查前端状态并给出相应提示
- 如果前端未启动，会提示如何启动前端

### 方式2: 再启动前端
```bash
start_frontend.bat
```
- 启动前端开发服务器
- 自动安装依赖（首次运行）
- 提供后端连接状态提示

## 📋 系统要求

### 必需环境
- **Python 3.8+**: 后端运行环境
- **Node.js 16+**: 前端开发环境
- **npm**: Node.js包管理器

### 检查环境
```bash
python --version
node --version
npm --version
```

## 🎯 功能特色

### 1. 知识图谱构建
- 📄 支持多种文件格式（PDF、DOCX、TXT等）
- 🤖 基于大语言模型的智能实体关系提取
- ⚡ 并行处理，快速构建

### 2. Wiki页面生成 ⭐
- 📖 自动生成维基百科风格的详细页面
- 🤖 使用豆包模型生成专业内容
- 🔄 图谱更新时自动同步Wiki

### 3. 可视化界面
- 🎨 交互式知识图谱可视化
- 📱 响应式设计，支持多设备
- 🔗 图谱与Wiki页面无缝切换

## 📖 使用流程

### 1. 上传文档
1. 访问前端界面: http://localhost:3000
2. 点击"上传文件"
3. 选择支持的文档格式
4. 等待上传完成

### 2. 构建图谱
1. 点击"处理文件"开始分析
2. 系统自动提取实体和关系
3. 同时生成对应的Wiki页面
4. 查看处理进度和结果

### 3. 查看结果
1. **图谱可视化**: 交互式查看实体关系
2. **Wiki页面**: 点击Wiki链接查看详细描述
3. **数据导出**: 支持JSON、CSV等格式

## 🔍 故障排除

### 常见问题

#### 1. 前端无法访问
**现象**: 浏览器显示"无法连接"
**解决**:
```bash
# 检查前端是否启动
start_frontend.bat

# 或重新启动完整系统
start_full_system.bat
```

#### 2. 后端API错误
**现象**: 前端显示API连接错误
**解决**:
```bash
# 检查后端状态
curl http://localhost:8000/health

# 重启后端
start_backend.bat
```

#### 3. 依赖安装失败
**现象**: 启动脚本报错
**解决**:
```bash
# 手动安装Python依赖
pip install -e .

# 手动安装前端依赖
cd frontend
npm install
```

#### 4. 端口被占用
**现象**: 启动时提示端口占用
**解决**:
```bash
# 查看端口占用
netstat -ano | findstr :8000
netstat -ano | findstr :3000

# 结束占用进程或修改配置
```

### 检查系统状态
访问: http://localhost:8000/api/system/status

```json
{
  "backend": {"status": "running"},
  "frontend": {"status": "running"},
  "services": {"wiki_generation": true}
}
```

## ⚙️ 配置说明

### 环境变量配置
复制 `.env.example` 到 `.env` 并修改:

```env
# LLM配置
LLM_API_KEY=your-api-key-here
LLM_MODEL=qwen2.5-32b-instruct-int4

# Wiki生成配置
ENABLE_WIKI_GENERATION=true
DOUBAO_MODEL=doubao-seed-1.6
```

### 重要配置项
- **LLM_API_KEY**: 大语言模型API密钥
- **ENABLE_WIKI_GENERATION**: 是否启用Wiki生成
- **WIKI_PARALLEL_PROCESSING**: 是否启用并行处理

## 🎨 高级功能

### 1. API调用
```bash
# 上传文件
curl -X POST "http://localhost:8000/api/files/upload" \
     -F "file=@document.pdf"

# 生成Wiki
curl -X POST "http://localhost:8000/api/wiki/generate" \
     -H "Content-Type: application/json" \
     -d '{"kg_id": "your-kg-id"}'
```

### 2. 批量处理
- 同时处理多个文件
- 合并生成统一知识图谱
- 批量生成Wiki页面

### 3. 图谱管理
- 保存和加载图谱
- 合并多个图谱
- 导出多种格式

## 📞 获取帮助

### 文档资源
- 📖 **完整文档**: README.md
- 🏗️ **架构说明**: POCKETFLOW_STRUCTURE.md
- 🌐 **Wiki功能**: WIKI_FEATURE_SUMMARY.md
- 🔧 **修复说明**: FRONTEND_CONNECTION_FIX.md

### 在线资源
- 📚 **API文档**: http://localhost:8000/docs
- 🔧 **系统状态**: http://localhost:8000/api/system/status
- 💡 **健康检查**: http://localhost:8000/health

### 技术支持
如遇问题，请检查:
1. 环境配置是否正确
2. 依赖是否完整安装
3. 端口是否被占用
4. API密钥是否有效

## 🎉 开始使用

现在您可以开始使用知识图谱系统了！

1. **运行** `start_full_system.bat`
2. **上传** 您的文档
3. **构建** 知识图谱
4. **查看** Wiki页面和可视化结果

享受智能知识图谱构建的强大功能！🚀
