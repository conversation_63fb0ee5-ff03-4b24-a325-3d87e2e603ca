@echo off
title 知识图谱系统 - 后端服务
echo ========================================
echo 启动知识图谱构建系统后端服务
echo ========================================
echo.

echo [1/3] 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH
    pause
    exit /b 1
)
echo ✅ Python环境正常

echo [2/3] 安装依赖...
pip install -e . >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ 依赖安装可能有问题，继续启动...
) else (
    echo ✅ 依赖安装完成
)

echo [3/3] 启动后端服务...
echo.
echo ========================================
echo 服务信息
echo ========================================
echo 🔗 后端地址: http://localhost:8000
echo 📚 API文档: http://localhost:8000/docs
echo 🌐 前端地址: http://localhost:3000
echo ========================================
echo.
echo 💡 提示:
echo   - 如需完整体验，请同时启动前端服务
echo   - 运行 start_frontend.bat 启动前端
echo   - 或运行 start_full_system.bat 同时启动前后端
echo.
echo 🚀 正在启动后端服务...
echo.

python -m app.main

echo.
echo 👋 后端服务已停止
pause
