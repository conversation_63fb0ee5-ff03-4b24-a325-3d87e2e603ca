"""
实体标准化服务
解决实体名称截断、不完整、重复等问题
"""
import re
import json
import difflib
from typing import List, Dict, Set, Tuple, Optional
from collections import defaultdict, Counter
from loguru import logger

from app.core.config import settings
from app.models.knowledge_graph import Entity, Relation
from app.services.llm_service import LLMService


class EntityStandardizationService:
    """实体标准化服务"""
    
    def __init__(self, llm_service: LLMService):
        self.llm_service = llm_service
        
        # 常见的不完整实体模式
        self.incomplete_patterns = [
            r'^.{1,2}$',  # 过短的实体（1-2个字符）
            r'.*[，。！？；：]$',  # 以标点符号结尾
            r'^[，。！？；：].*',  # 以标点符号开头
            r'.*\s+$',  # 以空格结尾
            r'^\s+.*',  # 以空格开头
            r'.*\d+$',  # 以数字结尾但不是完整的数字实体
        ]
        
        # 实体类型的常见后缀
        self.entity_suffixes = {
            'organization': ['公司', '企业', '集团', '机构', '组织', '团队', '部门', '中心'],
            'person': ['先生', '女士', '总', '经理', '主任', '博士', '教授'],
            'location': ['市', '省', '区', '县', '镇', '村', '路', '街', '号'],
            'product': ['系统', '平台', '软件', '工具', '服务', '产品'],
            'technology': ['技术', '算法', '框架', '协议', '标准', '方法']
        }
    
    async def standardize_entities(
        self, 
        entities: List[Entity], 
        relations: List[Relation],
        source_text: str = ""
    ) -> Tuple[List[Entity], List[Relation], Dict[str, str]]:
        """标准化实体列表"""
        if not settings.ENABLE_ENTITY_STANDARDIZATION:
            return entities, relations, {}
        
        logger.info(f"开始实体标准化处理，原始实体数: {len(entities)}")
        
        # 1. 检测和修复不完整实体
        completed_entities = await self._complete_incomplete_entities(entities, source_text)
        
        # 2. 验证实体有效性
        valid_entities = self._validate_entities(completed_entities)
        
        # 3. 标准化实体名称
        standardized_entities = self._standardize_entity_names(valid_entities)
        
        # 4. 去重和合并相似实体
        merged_entities, entity_mapping = self._merge_similar_entities(standardized_entities)
        
        # 5. 更新关系中的实体引用
        updated_relations = self._update_relations_with_mapping(relations, entity_mapping)
        
        logger.info(f"实体标准化完成: {len(entities)} -> {len(merged_entities)} 个实体")
        
        return merged_entities, updated_relations, entity_mapping
    
    async def _complete_incomplete_entities(self, entities: List[Entity], source_text: str) -> List[Entity]:
        """补全不完整的实体"""
        if not settings.ENABLE_ENTITY_COMPLETION:
            return entities
        
        completed_entities = []
        incomplete_entities = []
        
        for entity in entities:
            if self._is_incomplete_entity(entity):
                incomplete_entities.append(entity)
            else:
                completed_entities.append(entity)
        
        if incomplete_entities:
            logger.info(f"发现 {len(incomplete_entities)} 个不完整实体，尝试补全")
            
            # 使用LLM补全不完整实体
            completed = await self._complete_entities_with_llm(incomplete_entities, source_text)
            completed_entities.extend(completed)
        
        return completed_entities
    
    def _is_incomplete_entity(self, entity: Entity) -> bool:
        """判断实体是否不完整"""
        name = entity.name.strip()
        
        # 检查长度
        if len(name) < settings.MIN_ENTITY_NAME_LENGTH:
            return True
        
        if len(name) > settings.MAX_ENTITY_NAME_LENGTH:
            return True
        
        # 检查不完整模式
        for pattern in self.incomplete_patterns:
            if re.match(pattern, name):
                return True
        
        # 检查是否是常见的截断模式
        if self._is_likely_truncated(name, entity.type):
            return True
        
        return False
    
    def _is_likely_truncated(self, name: str, entity_type: str) -> bool:
        """判断实体名称是否可能被截断"""
        # 检查是否缺少常见后缀
        if entity_type.lower() in self.entity_suffixes:
            suffixes = self.entity_suffixes[entity_type.lower()]
            
            # 如果名称看起来应该有后缀但没有
            for suffix in suffixes:
                if name.endswith(suffix[:-1]) and not name.endswith(suffix):
                    return True
        
        # 检查是否以不完整的词结尾
        if re.search(r'[a-zA-Z]$', name) and len(name) > 3:
            # 英文单词可能被截断
            return True
        
        return False
    
    async def _complete_entities_with_llm(self, incomplete_entities: List[Entity], source_text: str) -> List[Entity]:
        """使用LLM补全不完整实体"""
        if not incomplete_entities:
            return []
        
        try:
            # 构建补全提示词
            entity_names = [e.name for e in incomplete_entities]
            
            prompt = f"""
请分析以下可能不完整的实体名称，并基于上下文补全它们。

不完整的实体名称：{', '.join(entity_names)}

上下文文本：
{source_text[:2000]}

请返回JSON格式的补全结果：
{{
    "completed_entities": [
        {{
            "original": "原始名称",
            "completed": "补全后的名称",
            "confidence": 0.9,
            "reason": "补全原因"
        }}
    ]
}}

要求：
1. 只补全确实不完整的实体
2. 补全后的名称必须在上下文中有依据
3. 如果无法确定补全方式，保持原名称不变
4. 置信度范围0.0-1.0

只返回JSON，不要其他内容。
"""
            
            messages = [
                {"role": "system", "content": "你是一个专业的实体补全专家，擅长根据上下文补全不完整的实体名称。"},
                {"role": "user", "content": prompt}
            ]
            
            response = await self.llm_service._call_llm(messages)
            
            # 解析JSON响应
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                data = json.loads(json_match.group())
                
                # 创建名称映射
                completion_map = {}
                for item in data.get("completed_entities", []):
                    original = item.get("original", "")
                    completed = item.get("completed", "")
                    confidence = item.get("confidence", 0.0)
                    
                    if confidence >= settings.ENTITY_COMPLETION_THRESHOLD:
                        completion_map[original] = completed
                
                # 应用补全
                completed_entities = []
                for entity in incomplete_entities:
                    if entity.name in completion_map:
                        # 创建补全后的实体
                        completed_entity = Entity(
                            id=entity.id,
                            name=completion_map[entity.name],
                            type=entity.type,
                            description=entity.description,
                            properties={
                                **entity.properties,
                                "original_name": entity.name,
                                "completed_by": "llm",
                                "completion_confidence": confidence
                            }
                        )
                        completed_entities.append(completed_entity)
                        logger.debug(f"实体补全: {entity.name} -> {completed_entity.name}")
                    else:
                        # 保持原实体
                        completed_entities.append(entity)
                
                return completed_entities
        
        except Exception as e:
            logger.error(f"LLM实体补全失败: {e}")
            return incomplete_entities
        
        return incomplete_entities
    
    def _validate_entities(self, entities: List[Entity]) -> List[Entity]:
        """验证实体有效性"""
        if not settings.ENABLE_ENTITY_VALIDATION:
            return entities
        
        valid_entities = []
        
        for entity in entities:
            if self._is_valid_entity(entity):
                valid_entities.append(entity)
            else:
                logger.debug(f"过滤无效实体: {entity.name}")
        
        logger.info(f"实体验证: {len(entities)} -> {len(valid_entities)} 个有效实体")
        return valid_entities
    
    def _is_valid_entity(self, entity: Entity) -> bool:
        """判断实体是否有效"""
        name = entity.name.strip()
        
        # 基本长度检查
        if len(name) < settings.MIN_ENTITY_NAME_LENGTH:
            return False
        
        if len(name) > settings.MAX_ENTITY_NAME_LENGTH:
            return False
        
        # 检查是否只包含标点符号
        if re.match(r'^[^\w\u4e00-\u9fff]+$', name):
            return False
        
        # 检查是否是无意义的字符串
        if re.match(r'^[0-9\s]+$', name) and len(name) < 4:
            return False
        
        return True
    
    def _standardize_entity_names(self, entities: List[Entity]) -> List[Entity]:
        """标准化实体名称"""
        standardized_entities = []
        
        for entity in entities:
            standardized_name = self._standardize_name(entity.name)
            
            if standardized_name != entity.name:
                # 创建标准化后的实体
                standardized_entity = Entity(
                    id=entity.id,
                    name=standardized_name,
                    type=entity.type,
                    description=entity.description,
                    properties={
                        **entity.properties,
                        "original_name": entity.name,
                        "standardized": True
                    }
                )
                standardized_entities.append(standardized_entity)
            else:
                standardized_entities.append(entity)
        
        return standardized_entities
    
    def _standardize_name(self, name: str) -> str:
        """标准化单个实体名称"""
        # 去除首尾空格
        name = name.strip()
        
        # 去除多余的空格
        name = re.sub(r'\s+', ' ', name)
        
        # 去除首尾的标点符号
        name = re.sub(r'^[^\w\u4e00-\u9fff]+|[^\w\u4e00-\u9fff]+$', '', name)
        
        # 统一引号
        name = name.replace('"', '"').replace('"', '"')
        name = name.replace(''', "'").replace(''', "'")
        
        return name
    
    def _merge_similar_entities(self, entities: List[Entity]) -> Tuple[List[Entity], Dict[str, str]]:
        """合并相似实体"""
        if len(entities) <= 1:
            return entities, {}
        
        # 构建相似度矩阵
        entity_groups = []
        entity_mapping = {}
        processed = set()
        
        for i, entity in enumerate(entities):
            if entity.id in processed:
                continue
            
            # 创建新组
            group = [entity]
            processed.add(entity.id)
            
            # 查找相似实体
            for j, other_entity in enumerate(entities[i+1:], i+1):
                if other_entity.id in processed:
                    continue
                
                similarity = self._calculate_entity_similarity(entity, other_entity)
                
                if similarity >= settings.ENTITY_SIMILARITY_THRESHOLD:
                    group.append(other_entity)
                    processed.add(other_entity.id)
            
            entity_groups.append(group)
        
        # 合并每个组
        merged_entities = []
        for group in entity_groups:
            if len(group) == 1:
                merged_entities.append(group[0])
                entity_mapping[group[0].id] = group[0].id
            else:
                merged_entity = self._merge_entity_group(group)
                merged_entities.append(merged_entity)
                
                # 建立映射
                for entity in group:
                    entity_mapping[entity.id] = merged_entity.id
        
        logger.info(f"实体合并: {len(entities)} -> {len(merged_entities)} 个实体")
        return merged_entities, entity_mapping
    
    def _calculate_entity_similarity(self, entity1: Entity, entity2: Entity) -> float:
        """计算实体相似度"""
        # 名称相似度（权重50%）
        name_similarity = difflib.SequenceMatcher(None, 
            entity1.name.lower(), 
            entity2.name.lower()
        ).ratio()
        
        # 类型相似度（权重30%）
        type_similarity = 1.0 if entity1.type == entity2.type else 0.0
        
        # 描述相似度（权重20%）
        desc_similarity = 0.0
        if entity1.description and entity2.description:
            desc_similarity = difflib.SequenceMatcher(None,
                entity1.description.lower(),
                entity2.description.lower()
            ).ratio()
        
        # 综合相似度
        total_similarity = (
            name_similarity * 0.5 + 
            type_similarity * 0.3 + 
            desc_similarity * 0.2
        )
        
        return total_similarity
    
    def _merge_entity_group(self, entities: List[Entity]) -> Entity:
        """合并一组相似实体"""
        # 选择最完整的实体作为主实体
        primary_entity = max(entities, key=lambda e: len(e.description or "") + len(e.name))
        
        # 合并信息
        merged_name = primary_entity.name
        merged_description = primary_entity.description or ""
        merged_properties = primary_entity.properties.copy()
        
        # 收集所有变体名称
        variant_names = []
        for entity in entities:
            if entity.name != primary_entity.name:
                variant_names.append(entity.name)
        
        if variant_names:
            merged_properties["variants"] = variant_names
        
        # 合并描述
        descriptions = set()
        for entity in entities:
            if entity.description:
                descriptions.add(entity.description)
        
        if len(descriptions) > 1:
            merged_description = "; ".join(descriptions)
        
        # 合并属性
        for entity in entities:
            merged_properties.update(entity.properties)
        
        return Entity(
            id=primary_entity.id,
            name=merged_name,
            type=primary_entity.type,
            description=merged_description,
            properties=merged_properties
        )
    
    def _update_relations_with_mapping(self, relations: List[Relation], entity_mapping: Dict[str, str]) -> List[Relation]:
        """使用实体映射更新关系"""
        updated_relations = []
        
        for relation in relations:
            # 更新实体ID
            source_id = entity_mapping.get(relation.source_entity, relation.source_entity)
            target_id = entity_mapping.get(relation.target_entity, relation.target_entity)
            
            # 创建更新后的关系
            updated_relation = Relation(
                id=relation.id,
                source_entity=source_id,
                target_entity=target_id,
                relation_type=relation.relation_type,
                confidence=relation.confidence,
                source_text=relation.source_text,
                properties=relation.properties
            )
            updated_relations.append(updated_relation)
        
        return updated_relations
    
    def create_entity_summary_file(self, entities: List[Entity], filename: str) -> str:
        """创建实体摘要临时文件"""
        if settings.ENTITY_TEMP_FILE_FORMAT == "structured":
            return self._create_structured_summary(entities, filename)
        else:
            return self._create_simple_summary(entities, filename)
    
    def _create_structured_summary(self, entities: List[Entity], filename: str) -> str:
        """创建结构化实体摘要"""
        # 按类型分组
        entities_by_type = defaultdict(list)
        for entity in entities:
            entities_by_type[entity.type].append(entity)
        
        summary_lines = [
            f"# {filename} - 实体摘要",
            f"总计实体数量: {len(entities)}",
            ""
        ]
        
        for entity_type, type_entities in sorted(entities_by_type.items()):
            summary_lines.extend([
                f"## {entity_type.upper()} ({len(type_entities)}个)",
                ""
            ])
            
            for entity in type_entities:
                summary_lines.append(f"- **{entity.name}**: {entity.description or '无描述'}")
            
            summary_lines.append("")
        
        return "\n".join(summary_lines)
    
    def _create_simple_summary(self, entities: List[Entity], filename: str) -> str:
        """创建简单实体摘要"""
        summary_lines = [
            f"文件 {filename} 中的主要实体:",
            ""
        ]
        
        for entity in entities:
            summary_lines.append(f"{entity.name} ({entity.type})")
        
        return "\n".join(summary_lines)
