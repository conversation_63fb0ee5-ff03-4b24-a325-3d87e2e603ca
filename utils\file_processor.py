"""
文件处理工具模块
提供文件上传、文本提取、文件类型处理等功能
"""
import os
import uuid
import aiofiles
from typing import List, Dict, Any, Optional
from pathlib import Path
import PyPDF2
from docx import Document
import pandas as pd
import json
import re
from datetime import datetime

from app.config import settings
from app.models import FileInfo, FileType, ProcessStatus, TextChunk


class FileProcessor:
    """文件处理器类"""
    
    def __init__(self):
        self.upload_dir = Path(settings.UPLOAD_DIR)
        self.upload_dir.mkdir(exist_ok=True)
        self._file_cache: Dict[str, FileInfo] = {}
    
    async def save_uploaded_file(self, file_content: bytes, filename: str) -> FileInfo:
        """保存上传的文件"""
        # 生成唯一文件ID和路径
        file_id = str(uuid.uuid4())
        file_extension = Path(filename).suffix.lower()
        
        # 验证文件类型
        if file_extension not in settings.ALLOWED_EXTENSIONS:
            raise ValueError(f"不支持的文件类型: {file_extension}")
        
        # 验证文件大小
        if len(file_content) > settings.MAX_FILE_SIZE:
            raise ValueError(f"文件大小超过限制: {len(file_content)} bytes")
        
        # 生成新文件名
        new_filename = f"{file_id}{file_extension}"
        file_path = self.upload_dir / new_filename
        
        # 保存文件
        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(file_content)
        
        # 确定文件类型
        file_type = self._get_file_type(file_extension)
        
        # 创建文件信息
        file_info = FileInfo(
            id=file_id,
            filename=new_filename,
            original_name=filename,
            file_type=file_type,
            file_size=len(file_content),
            file_path=str(file_path),
            status=ProcessStatus.PENDING
        )
        
        # 缓存文件信息
        self._file_cache[file_id] = file_info
        
        return file_info
    
    def _get_file_type(self, extension: str) -> FileType:
        """根据扩展名确定文件类型"""
        extension = extension.lower()
        type_mapping = {
            '.txt': FileType.TXT,
            '.pdf': FileType.PDF,
            '.docx': FileType.DOCX,
            '.doc': FileType.DOC,
            '.xlsx': FileType.XLSX,
            '.xls': FileType.XLS,
            '.csv': FileType.CSV,
            '.json': FileType.JSON
        }
        return type_mapping.get(extension, FileType.TXT)
    
    async def get_file_info(self, file_id: str) -> Optional[FileInfo]:
        """获取文件信息"""
        if file_id in self._file_cache:
            return self._file_cache[file_id]
        
        # 尝试从文件系统查找
        for file_path in self.upload_dir.glob(f"{file_id}.*"):
            if file_path.is_file():
                # 重建文件信息
                file_info = FileInfo(
                    id=file_id,
                    filename=file_path.name,
                    original_name=file_path.name,
                    file_type=self._get_file_type(file_path.suffix),
                    file_size=file_path.stat().st_size,
                    file_path=str(file_path),
                    status=ProcessStatus.PENDING
                )
                self._file_cache[file_id] = file_info
                return file_info
        
        return None
    
    async def extract_text_from_file(self, file_info: FileInfo) -> str:
        """从文件中提取文本"""
        try:
            if file_info.file_type == FileType.TXT:
                return await self._extract_from_txt(file_info.file_path)
            elif file_info.file_type == FileType.PDF:
                return await self._extract_from_pdf(file_info.file_path)
            elif file_info.file_type == FileType.DOCX:
                return await self._extract_from_docx(file_info.file_path)
            elif file_info.file_type in [FileType.XLSX, FileType.XLS]:
                return await self._extract_from_excel(file_info.file_path)
            elif file_info.file_type == FileType.CSV:
                return await self._extract_from_csv(file_info.file_path)
            elif file_info.file_type == FileType.JSON:
                return await self._extract_from_json(file_info.file_path)
            else:
                raise ValueError(f"不支持的文件类型: {file_info.file_type}")
        except Exception as e:
            raise Exception(f"文本提取失败 {file_info.filename}: {e}")
    
    async def _extract_from_txt(self, file_path: str) -> str:
        """从TXT文件提取文本"""
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
            return await f.read()
    
    async def _extract_from_pdf(self, file_path: str) -> str:
        """从PDF文件提取文本"""
        text = ""
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
        return text
    
    async def _extract_from_docx(self, file_path: str) -> str:
        """从DOCX文件提取文本"""
        doc = Document(file_path)
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        return text
    
    async def _extract_from_excel(self, file_path: str) -> str:
        """从Excel文件提取文本"""
        df = pd.read_excel(file_path)
        return df.to_string()
    
    async def _extract_from_csv(self, file_path: str) -> str:
        """从CSV文件提取文本"""
        df = pd.read_csv(file_path)
        return df.to_string()
    
    async def _extract_from_json(self, file_path: str) -> str:
        """从JSON文件提取文本"""
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
            content = await f.read()
            data = json.loads(content)
            return json.dumps(data, ensure_ascii=False, indent=2)
    
    def split_text_into_chunks(
        self, 
        text: str, 
        chunk_size: int = None, 
        overlap: int = None
    ) -> List[TextChunk]:
        """将文本分割为块（优化版，避免截断实体）"""
        if chunk_size is None:
            chunk_size = settings.CHUNK_SIZE
        if overlap is None:
            overlap = settings.CHUNK_OVERLAP

        chunks = []
        start = 0
        chunk_id = 0

        while start < len(text):
            end = min(start + chunk_size, len(text))

            # 如果不是最后一块，寻找安全的分割点
            if end < len(text):
                safe_end = self._find_safe_split_point(text, start, end)
                if safe_end > start:
                    end = safe_end

            chunk_content = text[start:end].strip()
            if chunk_content:
                chunk = TextChunk(
                    id=f"chunk_{chunk_id}",
                    content=chunk_content,
                    start_pos=start,
                    end_pos=end
                )
                chunks.append(chunk)
                chunk_id += 1

            start = max(start + 1, end - overlap)

        return chunks

    def _find_safe_split_point(self, text: str, start: int, end: int) -> int:
        """找到安全的分割点，避免截断实体名称"""
        # 优先级分割点列表
        delimiters = [
            ('\n\n', 2),      # 段落分隔符（最高优先级）
            ('。', 1),         # 句号
            ('！', 1),         # 感叹号
            ('？', 1),         # 问号
            ('\n', 1),        # 换行符
            ('；', 1),         # 分号
            ('，', 1),         # 逗号（较低优先级）
        ]

        best_split = end

        # 从后往前查找分割点
        for delimiter, priority in delimiters:
            # 在一定范围内查找分割符
            search_start = max(start, end - 200)  # 向前搜索200字符
            last_delimiter = text.rfind(delimiter, search_start, end)

            if last_delimiter > start:
                candidate_end = last_delimiter + len(delimiter)

                # 检查这个分割点是否安全（不会截断实体）
                if self._is_safe_split_point(text, candidate_end):
                    best_split = candidate_end
                    break

        # 如果没有找到安全的分割点，尝试在词边界分割
        if best_split == end:
            word_boundary = self._find_word_boundary(text, start, end)
            if word_boundary > start:
                best_split = word_boundary

        return best_split

    def _is_safe_split_point(self, text: str, split_pos: int) -> bool:
        """检查分割点是否安全（不会截断实体）"""
        # 检查分割点前后的文本，确保不会截断实体名称

        # 向前检查20个字符
        before_text = text[max(0, split_pos - 20):split_pos]
        # 向后检查20个字符
        after_text = text[split_pos:min(len(text), split_pos + 20)]

        # 检查是否在引号、括号等内部
        if self._is_inside_quotes_or_brackets(before_text, after_text):
            return False

        # 检查是否可能截断专有名词
        if self._might_truncate_proper_noun(before_text, after_text):
            return False

        return True

    def _is_inside_quotes_or_brackets(self, before_text: str, after_text: str) -> bool:
        """检查是否在引号或括号内部"""
        # 检查引号
        quote_chars = ['"', "'", '"', '"', ''', ''']
        for quote in quote_chars:
            if before_text.count(quote) % 2 == 1:
                return True

        # 检查括号
        bracket_pairs = [('(', ')'), ('（', '）'), ('[', ']'), ('【', '】')]
        for open_bracket, close_bracket in bracket_pairs:
            open_count = before_text.count(open_bracket)
            close_count = before_text.count(close_bracket)
            if open_count > close_count:
                return True

        return False

    def _might_truncate_proper_noun(self, before_text: str, after_text: str) -> bool:
        """检查是否可能截断专有名词"""
        # 简单的启发式规则：如果前后都是中文字符或字母，可能是专有名词
        if before_text and after_text:
            last_char = before_text[-1]
            first_char = after_text[0]

            # 中文字符连续
            if '\u4e00' <= last_char <= '\u9fff' and '\u4e00' <= first_char <= '\u9fff':
                return True

            # 英文字母连续
            if last_char.isalpha() and first_char.isalpha():
                return True

        return False

    def _find_word_boundary(self, text: str, start: int, end: int) -> int:
        """找到词边界"""
        # 从后往前查找空格或标点符号
        for i in range(end - 1, start, -1):
            char = text[i]
            if char.isspace() or char in '，。！？；：':
                return i + 1
        return end

    async def create_temp_file(self, content: str, prefix: str = "temp") -> str:
        """创建临时文件"""
        temp_id = str(uuid.uuid4())
        temp_filename = f"{prefix}_{temp_id}.txt"
        temp_path = self.upload_dir / temp_filename

        async with aiofiles.open(temp_path, 'w', encoding='utf-8') as f:
            await f.write(content)

        return str(temp_path)

    async def delete_file(self, file_path: str) -> bool:
        """删除文件"""
        try:
            path = Path(file_path)
            if path.exists():
                path.unlink()
                return True
            else:
                return False
        except Exception as e:
            return False

    async def cleanup_uploads_directory(self) -> int:
        """清理uploads目录中的所有文件"""
        deleted_count = 0
        try:
            for file_path in self.upload_dir.glob('*'):
                if file_path.is_file():
                    file_path.unlink()
                    deleted_count += 1
        except Exception as e:
            pass
        return deleted_count

    async def scan_folder(self, folder_path: str) -> Dict[str, Any]:
        """扫描文件夹"""
        folder = Path(folder_path)
        if not folder.exists() or not folder.is_dir():
            raise ValueError(f"文件夹不存在: {folder_path}")

        supported_files = []
        unsupported_files = []
        total_size = 0

        for file_path in folder.rglob('*'):
            if file_path.is_file():
                file_size = file_path.stat().st_size
                total_size += file_size

                if file_path.suffix.lower() in settings.ALLOWED_EXTENSIONS:
                    supported_files.append(str(file_path))
                else:
                    unsupported_files.append(str(file_path))

        return {
            "total_files": len(supported_files) + len(unsupported_files),
            "supported_files": supported_files,
            "unsupported_files": unsupported_files,
            "total_size": total_size
        }

    def get_file_stats(self) -> Dict[str, Any]:
        """获取文件处理统计信息"""
        total_files = len(self._file_cache)
        file_types = {}
        total_size = 0

        for file_info in self._file_cache.values():
            file_type = file_info.file_type.value
            file_types[file_type] = file_types.get(file_type, 0) + 1
            total_size += file_info.file_size

        return {
            "total_files": total_files,
            "file_types": file_types,
            "total_size": total_size,
            "cache_size": len(self._file_cache)
        }
