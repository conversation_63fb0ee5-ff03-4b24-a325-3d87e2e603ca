"""
进度跟踪服务
"""
import asyncio
import time
from typing import Dict, Optional, Callable
from dataclasses import dataclass
from enum import Enum
from loguru import logger


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class ProgressInfo:
    """进度信息"""
    task_id: str
    task_name: str
    status: TaskStatus
    current: int
    total: int
    percentage: float
    start_time: float
    end_time: Optional[float] = None
    error_message: Optional[str] = None
    
    @property
    def elapsed_time(self) -> float:
        """已用时间（秒）"""
        end = self.end_time or time.time()
        return end - self.start_time
    
    @property
    def estimated_remaining(self) -> Optional[float]:
        """预估剩余时间（秒）"""
        if self.current == 0 or self.status != TaskStatus.RUNNING:
            return None
        
        elapsed = self.elapsed_time
        rate = self.current / elapsed
        remaining_items = self.total - self.current
        
        return remaining_items / rate if rate > 0 else None


class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self):
        self.tasks: Dict[str, ProgressInfo] = {}
        self.callbacks: Dict[str, list] = {}
        self._lock = asyncio.Lock()
    
    async def create_task(
        self, 
        task_id: str, 
        task_name: str, 
        total: int
    ) -> ProgressInfo:
        """创建新任务"""
        async with self._lock:
            progress = ProgressInfo(
                task_id=task_id,
                task_name=task_name,
                status=TaskStatus.PENDING,
                current=0,
                total=total,
                percentage=0.0,
                start_time=time.time()
            )
            self.tasks[task_id] = progress
            logger.info(f"创建进度跟踪任务: {task_name} (ID: {task_id})")
            return progress
    
    async def start_task(self, task_id: str):
        """开始任务"""
        async with self._lock:
            if task_id in self.tasks:
                self.tasks[task_id].status = TaskStatus.RUNNING
                self.tasks[task_id].start_time = time.time()
                await self._notify_callbacks(task_id)
    
    async def update_progress(
        self, 
        task_id: str, 
        current: int, 
        message: Optional[str] = None
    ):
        """更新进度"""
        async with self._lock:
            if task_id not in self.tasks:
                return
            
            progress = self.tasks[task_id]
            progress.current = min(current, progress.total)
            progress.percentage = (progress.current / progress.total) * 100 if progress.total > 0 else 0
            
            if message:
                logger.info(f"任务 {task_id}: {message} ({progress.current}/{progress.total})")
            
            await self._notify_callbacks(task_id)
    
    async def complete_task(self, task_id: str, success: bool = True, error_message: str = None):
        """完成任务"""
        async with self._lock:
            if task_id not in self.tasks:
                return
            
            progress = self.tasks[task_id]
            progress.status = TaskStatus.COMPLETED if success else TaskStatus.FAILED
            progress.end_time = time.time()
            progress.error_message = error_message
            
            if success:
                progress.current = progress.total
                progress.percentage = 100.0
            
            logger.info(f"任务完成: {progress.task_name} - {'成功' if success else '失败'}")
            await self._notify_callbacks(task_id)
    
    async def cancel_task(self, task_id: str):
        """取消任务"""
        async with self._lock:
            if task_id not in self.tasks:
                return
            
            progress = self.tasks[task_id]
            progress.status = TaskStatus.CANCELLED
            progress.end_time = time.time()
            
            logger.info(f"任务已取消: {progress.task_name}")
            await self._notify_callbacks(task_id)
    
    def get_progress(self, task_id: str) -> Optional[ProgressInfo]:
        """获取任务进度"""
        return self.tasks.get(task_id)
    
    def get_all_tasks(self) -> Dict[str, ProgressInfo]:
        """获取所有任务"""
        return self.tasks.copy()
    
    def add_callback(self, task_id: str, callback: Callable[[ProgressInfo], None]):
        """添加进度回调"""
        if task_id not in self.callbacks:
            self.callbacks[task_id] = []
        self.callbacks[task_id].append(callback)
    
    async def _notify_callbacks(self, task_id: str):
        """通知回调函数"""
        if task_id in self.callbacks and task_id in self.tasks:
            progress = self.tasks[task_id]
            for callback in self.callbacks[task_id]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(progress)
                    else:
                        callback(progress)
                except Exception as e:
                    logger.error(f"进度回调执行失败: {e}")
    
    async def cleanup_completed_tasks(self, max_age_hours: int = 24):
        """清理已完成的旧任务"""
        async with self._lock:
            current_time = time.time()
            to_remove = []
            
            for task_id, progress in self.tasks.items():
                if progress.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                    age_hours = (current_time - progress.start_time) / 3600
                    if age_hours > max_age_hours:
                        to_remove.append(task_id)
            
            for task_id in to_remove:
                del self.tasks[task_id]
                if task_id in self.callbacks:
                    del self.callbacks[task_id]
            
            if to_remove:
                logger.info(f"清理了 {len(to_remove)} 个过期任务")


# 全局进度跟踪器实例
progress_tracker = ProgressTracker()
