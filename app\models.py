"""
Pydantic 请求/响应模型
"""
from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from enum import Enum
from pydantic import BaseModel, Field


# ==================== 枚举类型 ====================
class FileType(str, Enum):
    """文件类型枚举"""
    TXT = "txt"
    PDF = "pdf"
    DOCX = "docx"
    DOC = "doc"
    XLSX = "xlsx"
    XLS = "xls"
    CSV = "csv"
    JSON = "json"


class ProcessStatus(str, Enum):
    """处理状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class AnalysisType(str, Enum):
    """分析类型枚举"""
    ENTITIES_RELATIONS = "entities_relations"
    SUMMARY = "summary"


# ==================== 基础数据模型 ====================
class Entity(BaseModel):
    """实体模型"""
    name: str = Field(..., description="实体名称")
    type: str = Field(..., description="实体类型")
    description: Optional[str] = Field(None, description="实体描述")
    properties: Dict[str, Any] = Field(default_factory=dict, description="实体属性")
    confidence: float = Field(default=1.0, description="置信度")


class Relation(BaseModel):
    """关系模型"""
    source: str = Field(..., description="源实体")
    target: str = Field(..., description="目标实体")
    relation: str = Field(..., description="关系类型")
    description: Optional[str] = Field(None, description="关系描述")
    properties: Dict[str, Any] = Field(default_factory=dict, description="关系属性")
    confidence: float = Field(default=1.0, description="置信度")


class TextChunk(BaseModel):
    """文本块模型"""
    id: str = Field(..., description="文本块ID")
    content: str = Field(..., description="文本内容")
    start_pos: int = Field(..., description="起始位置")
    end_pos: int = Field(..., description="结束位置")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class FileInfo(BaseModel):
    """文件信息模型"""
    id: str = Field(..., description="文件ID")
    filename: str = Field(..., description="文件名")
    original_name: str = Field(..., description="原始文件名")
    file_type: FileType = Field(..., description="文件类型")
    file_size: int = Field(..., description="文件大小")
    file_path: str = Field(..., description="文件路径")
    status: ProcessStatus = Field(default=ProcessStatus.PENDING, description="处理状态")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")


class KnowledgeGraph(BaseModel):
    """知识图谱模型"""
    id: str = Field(..., description="图谱ID")
    name: str = Field(..., description="图谱名称")
    entities: List[Entity] = Field(default_factory=list, description="实体列表")
    relations: List[Relation] = Field(default_factory=list, description="关系列表")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")


# ==================== 请求模型 ====================
class TextAnalysisRequest(BaseModel):
    """文本分析请求"""
    text: str = Field(..., description="待分析文本")
    analysis_type: AnalysisType = Field(..., description="分析类型")
    options: Dict[str, Any] = Field(default_factory=dict, description="分析选项")


class FileUploadRequest(BaseModel):
    """文件上传请求"""
    filename: str = Field(..., description="文件名")
    file_size: int = Field(..., description="文件大小")


class KnowledgeGraphRequest(BaseModel):
    """知识图谱请求"""
    name: str = Field(..., description="图谱名称")
    description: Optional[str] = Field(None, description="图谱描述")
    options: Dict[str, Any] = Field(default_factory=dict, description="构建选项")


# ==================== 响应模型 ====================
class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")


class TextAnalysisResponse(BaseResponse):
    """文本分析响应"""
    analysis_type: AnalysisType = Field(..., description="分析类型")
    result: Dict[str, Any] = Field(..., description="分析结果")


class FileUploadResponse(BaseResponse):
    """文件上传响应"""
    file_info: FileInfo = Field(..., description="文件信息")


class ProcessResult(BaseModel):
    """处理结果模型"""
    file_id: str = Field(..., description="文件ID")
    status: ProcessStatus = Field(..., description="处理状态")
    text_chunks: List[TextChunk] = Field(default_factory=list, description="文本块")
    extracted_entities: List[Dict[str, Any]] = Field(default_factory=list, description="提取的实体")
    extracted_relations: List[Dict[str, Any]] = Field(default_factory=list, description="提取的关系")
    error_message: Optional[str] = Field(None, description="错误信息")
    processing_time: Optional[float] = Field(None, description="处理时间")


class KnowledgeGraphResponse(BaseResponse):
    """知识图谱响应"""
    knowledge_graph: KnowledgeGraph = Field(..., description="知识图谱")


class ProgressInfo(BaseModel):
    """进度信息模型"""
    task_id: str = Field(..., description="任务ID")
    status: ProcessStatus = Field(..., description="任务状态")
    progress: float = Field(default=0.0, description="进度百分比")
    current_step: str = Field(default="", description="当前步骤")
    total_steps: int = Field(default=1, description="总步骤数")
    completed_steps: int = Field(default=0, description="已完成步骤数")
    start_time: datetime = Field(default_factory=datetime.now, description="开始时间")
    estimated_time: Optional[float] = Field(None, description="预估剩余时间")
    error_message: Optional[str] = Field(None, description="错误信息")


# ==================== 扩展模型 ====================
class BatchProcessRequest(BaseModel):
    """批量处理请求模型"""
    file_ids: List[str] = Field(..., description="文件ID列表")
    options: Dict[str, Any] = Field(default_factory=dict, description="处理选项")


class FolderScanResult(BaseModel):
    """文件夹扫描结果"""
    total_files: int = Field(..., description="总文件数")
    supported_files: List[str] = Field(default_factory=list, description="支持的文件列表")
    unsupported_files: List[str] = Field(default_factory=list, description="不支持的文件列表")
    total_size: int = Field(..., description="总大小(字节)")


class EntityClassificationRequest(BaseModel):
    """实体分类请求"""
    entity_name: str = Field(..., description="实体名称")
    context: str = Field(default="", description="上下文")


class GraphSearchResult(BaseModel):
    """图搜索结果"""
    entities: List[Entity] = Field(default_factory=list, description="匹配的实体")
    relations: List[Relation] = Field(default_factory=list, description="相关关系")
    subgraph: Optional[Dict[str, Any]] = Field(None, description="子图数据")


class GraphStats(BaseModel):
    """图统计信息"""
    total_entities: int = Field(..., description="实体总数")
    total_relations: int = Field(..., description="关系总数")
    entity_types: Dict[str, int] = Field(default_factory=dict, description="实体类型统计")
    relation_types: Dict[str, int] = Field(default_factory=dict, description="关系类型统计")
    avg_degree: float = Field(default=0.0, description="平均度数")
    density: float = Field(default=0.0, description="图密度")


# ==================== Wiki 相关模型 ====================
class WikiPageInfo(BaseModel):
    """Wiki页面信息"""
    kg_id: str = Field(..., description="知识图谱ID")
    kg_name: str = Field(..., description="知识图谱名称")
    url: str = Field(..., description="Wiki页面URL")
    generated_at: str = Field(..., description="生成时间")
    last_updated: str = Field(..., description="最后更新时间")
    entity_count: int = Field(..., description="实体数量")
    relation_count: int = Field(..., description="关系数量")


class WikiGenerationRequest(BaseModel):
    """Wiki生成请求"""
    kg_id: str = Field(..., description="知识图谱ID")
    force_regenerate: bool = Field(default=False, description="是否强制重新生成")
    include_detailed_descriptions: bool = Field(default=True, description="是否包含详细描述")


class WikiGenerationResponse(BaseResponse):
    """Wiki生成响应"""
    wiki_url: str = Field(..., description="Wiki页面URL")
    generation_time: float = Field(..., description="生成耗时(秒)")
    entity_count: int = Field(..., description="处理的实体数量")


class WikiContent(BaseModel):
    """Wiki内容模型"""
    kg_id: str = Field(..., description="知识图谱ID")
    kg_name: str = Field(..., description="知识图谱名称")
    overview: str = Field(..., description="概述")
    entity_descriptions: Dict[str, str] = Field(default_factory=dict, description="实体描述")
    network_description: str = Field(..., description="网络描述")
    statistics: Dict[str, Any] = Field(default_factory=dict, description="统计信息")
    generated_at: str = Field(..., description="生成时间")
    last_updated: str = Field(..., description="最后更新时间")
