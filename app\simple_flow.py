"""
简单的 PocketFlow 工作流示例
包含知识图谱构建的完整流程和追踪
"""
import asyncio
import time
from typing import List, Dict, Any, Optional
from datetime import datetime

from .models import Entity, Relation, KnowledgeGraph, ProgressInfo
from .config import settings
from utils.file_processor import FileProcessor
from utils.llm_service import LLMService
from utils.knowledge_graph_builder import KnowledgeGraphBuilder
from utils.progress_tracker import ProgressTracker


class KnowledgeGraphFlow:
    """知识图谱构建工作流"""
    
    def __init__(self):
        self.file_processor = FileProcessor()
        self.llm_service = LLMService()
        self.kg_builder = KnowledgeGraphBuilder()
        self.progress_tracker = ProgressTracker()
    
    async def execute_simple_flow(
        self,
        file_path: str,
        kg_name: Optional[str] = None,
        task_id: Optional[str] = None
    ) -> KnowledgeGraph:
        """
        执行简单的知识图谱构建流程
        
        Args:
            file_path: 文件路径
            kg_name: 知识图谱名称
            task_id: 任务ID
            
        Returns:
            构建完成的知识图谱
        """
        if not task_id:
            task_id = f"flow_{int(time.time())}"
        
        if not kg_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            kg_name = f"KG_{timestamp}"
        
        try:
            # 步骤1: 初始化进度跟踪
            await self.progress_tracker.start_task(
                task_id, 
                "知识图谱构建流程",
                total_steps=5
            )
            
            # 步骤2: 文件处理
            await self.progress_tracker.update_progress(
                task_id, 
                step="文件处理",
                progress=0.1
            )
            
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()
            
            if not text.strip():
                raise ValueError("文件内容为空")
            
            # 步骤3: 文本预处理
            await self.progress_tracker.update_progress(
                task_id,
                step="文本预处理",
                progress=0.3
            )
            
            # 分割文本
            chunks = self.file_processor.split_text_into_chunks(text)
            
            # 步骤4: 实体关系提取
            await self.progress_tracker.update_progress(
                task_id,
                step="实体关系提取",
                progress=0.5
            )
            
            all_entities = []
            all_relations = []
            
            # 批量处理文本块
            for i, chunk in enumerate(chunks):
                entities, relations = await self.llm_service.extract_entities_and_relations(
                    chunk.content
                )
                all_entities.extend(entities)
                all_relations.extend(relations)
                
                # 更新进度
                chunk_progress = 0.5 + (i + 1) / len(chunks) * 0.3
                await self.progress_tracker.update_progress(
                    task_id,
                    step=f"处理文本块 {i+1}/{len(chunks)}",
                    progress=chunk_progress
                )
            
            # 步骤5: 知识图谱构建
            await self.progress_tracker.update_progress(
                task_id,
                step="知识图谱构建",
                progress=0.8
            )
            
            # 创建知识图谱
            kg = KnowledgeGraph(
                id=task_id,
                name=kg_name,
                entities=all_entities,
                relations=all_relations,
                metadata={
                    "source_file": file_path,
                    "total_chunks": len(chunks),
                    "creation_method": "simple_flow",
                    "flow_version": "1.0"
                }
            )
            
            # 步骤6: 保存图谱
            await self.progress_tracker.update_progress(
                task_id,
                step="保存知识图谱",
                progress=0.9
            )
            
            await self.kg_builder.save_knowledge_graph(kg)
            
            # 完成任务
            await self.progress_tracker.complete_task(task_id, success=True)
            
            return kg
            
        except Exception as e:
            await self.progress_tracker.complete_task(
                task_id, 
                success=False, 
                error=str(e)
            )
            raise
    
    async def execute_batch_flow(
        self,
        file_paths: List[str],
        kg_name: Optional[str] = None,
        task_id: Optional[str] = None
    ) -> KnowledgeGraph:
        """
        执行批量文件的知识图谱构建流程
        
        Args:
            file_paths: 文件路径列表
            kg_name: 知识图谱名称
            task_id: 任务ID
            
        Returns:
            构建完成的知识图谱
        """
        if not task_id:
            task_id = f"batch_flow_{int(time.time())}"
        
        if not kg_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            kg_name = f"KG_Batch_{timestamp}"
        
        try:
            # 初始化进度跟踪
            total_steps = len(file_paths) + 2  # 文件处理 + 合并 + 保存
            await self.progress_tracker.start_task(
                task_id,
                "批量知识图谱构建流程",
                total_steps=total_steps
            )
            
            all_entities = []
            all_relations = []
            
            # 处理每个文件
            for i, file_path in enumerate(file_paths):
                await self.progress_tracker.update_progress(
                    task_id,
                    step=f"处理文件 {i+1}/{len(file_paths)}: {file_path}",
                    progress=(i + 1) / total_steps
                )
                
                # 读取文件
                with open(file_path, 'r', encoding='utf-8') as f:
                    text = f.read()
                
                if text.strip():
                    # 提取实体关系
                    entities, relations = await self.llm_service.extract_entities_and_relations(text)
                    all_entities.extend(entities)
                    all_relations.extend(relations)
            
            # 合并和去重
            await self.progress_tracker.update_progress(
                task_id,
                step="合并和去重实体关系",
                progress=0.9
            )
            
            # 简单去重（基于名称）
            unique_entities = []
            entity_names = set()
            for entity in all_entities:
                if entity.name not in entity_names:
                    unique_entities.append(entity)
                    entity_names.add(entity.name)
            
            # 创建知识图谱
            kg = KnowledgeGraph(
                id=task_id,
                name=kg_name,
                entities=unique_entities,
                relations=all_relations,
                metadata={
                    "source_files": file_paths,
                    "total_files": len(file_paths),
                    "creation_method": "batch_flow",
                    "flow_version": "1.0"
                }
            )
            
            # 保存图谱
            await self.progress_tracker.update_progress(
                task_id,
                step="保存知识图谱",
                progress=0.95
            )
            
            await self.kg_builder.save_knowledge_graph(kg)
            
            # 完成任务
            await self.progress_tracker.complete_task(task_id, success=True)
            
            return kg
            
        except Exception as e:
            await self.progress_tracker.complete_task(
                task_id,
                success=False,
                error=str(e)
            )
            raise
    
    async def get_flow_status(self, task_id: str) -> Optional[ProgressInfo]:
        """获取工作流状态"""
        return await self.progress_tracker.get_progress(task_id)
    
    async def list_active_flows(self) -> List[ProgressInfo]:
        """列出活跃的工作流"""
        return await self.progress_tracker.list_active_tasks()


# 创建全局工作流实例
knowledge_graph_flow = KnowledgeGraphFlow()


# 便捷函数
async def build_kg_from_file(file_path: str, kg_name: Optional[str] = None) -> KnowledgeGraph:
    """从单个文件构建知识图谱的便捷函数"""
    return await knowledge_graph_flow.execute_simple_flow(file_path, kg_name)


async def build_kg_from_files(file_paths: List[str], kg_name: Optional[str] = None) -> KnowledgeGraph:
    """从多个文件构建知识图谱的便捷函数"""
    return await knowledge_graph_flow.execute_batch_flow(file_paths, kg_name)
