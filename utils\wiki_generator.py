"""
Wiki 网页生成器工具模块
为知识图谱生成对应的 Wiki 风格网页
"""
import asyncio
import json
import uuid
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import httpx
from jinja2 import Environment, FileSystemLoader, Template

from app.config import settings
from app.models import Entity, Relation, KnowledgeGraph


class WikiGenerator:
    """Wiki 网页生成器类"""
    
    def __init__(self):
        self.wiki_dir = Path(settings.WIKI_OUTPUT_DIR)
        self.template_dir = Path(settings.WIKI_TEMPLATE_DIR)
        self.wiki_dir.mkdir(exist_ok=True)
        self.template_dir.mkdir(exist_ok=True)
        
        # 创建模板环境
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(self.template_dir)),
            autoescape=True
        )
        
        # HTTP客户端配置
        self.client_config = {
            "timeout": httpx.Timeout(settings.LLM_REQUEST_TIMEOUT),
            "limits": httpx.Limits(
                max_connections=settings.CONNECTION_POOL_SIZE,
                max_keepalive_connections=settings.CONNECTION_POOL_SIZE // 2
            )
        }
        self._client = None
        
        # 并发控制
        self.semaphore = asyncio.Semaphore(settings.WIKI_MAX_CONCURRENT)
    
    async def get_client(self) -> httpx.AsyncClient:
        """获取HTTP客户端实例"""
        if self._client is None or self._client.is_closed:
            self._client = httpx.AsyncClient(**self.client_config)
        return self._client
    
    async def close_client(self):
        """关闭HTTP客户端"""
        if self._client and not self._client.is_closed:
            await self._client.aclose()
            self._client = None
    
    async def _call_doubao_llm(self, messages: List[Dict[str, str]]) -> str:
        """调用豆包模型API"""
        headers = {
            "Authorization": f"Bearer {settings.LLM_API_KEY}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": settings.DOUBAO_MODEL,
            "messages": messages,
            "max_tokens": settings.DOUBAO_MAX_TOKENS,
            "temperature": settings.DOUBAO_TEMPERATURE
        }
        
        async with self.semaphore:
            client = await self.get_client()
            
            for attempt in range(settings.LLM_MAX_RETRIES):
                try:
                    response = await client.post(
                        f"{settings.LLM_API_BASE}/chat/completions",
                        headers=headers,
                        json=payload
                    )
                    response.raise_for_status()
                    result = response.json()
                    return result["choices"][0]["message"]["content"]
                
                except Exception as e:
                    if attempt < settings.LLM_MAX_RETRIES - 1:
                        await asyncio.sleep(settings.LLM_RETRY_DELAY * (attempt + 1))
                    else:
                        raise Exception(f"豆包模型API调用失败: {e}")
    
    async def generate_entity_description(self, entity: Entity, related_entities: List[Entity], relations: List[Relation]) -> str:
        """为实体生成详细描述"""
        # 收集相关信息
        related_info = []
        for relation in relations:
            if relation.source == entity.name:
                related_info.append(f"与 {relation.target} 的关系: {relation.relation}")
            elif relation.target == entity.name:
                related_info.append(f"与 {relation.source} 的关系: {relation.relation}")
        
        related_entities_names = [e.name for e in related_entities if e.name != entity.name]
        
        prompt = f"""
请为以下实体生成一个详细的、类似维基百科风格的描述。

实体信息：
- 名称：{entity.name}
- 类型：{entity.type}
- 基础描述：{entity.description or "无"}

相关关系：
{chr(10).join(related_info) if related_info else "无直接关系"}

相关实体：
{', '.join(related_entities_names[:10]) if related_entities_names else "无"}

要求：
1. 生成一个详细的、结构化的描述（300-800字）
2. 包含实体的定义、特征、重要性等
3. 如果有相关关系，请在描述中自然地提及
4. 使用维基百科的写作风格：客观、准确、信息丰富
5. 分段组织内容，使用适当的标题
6. 如果信息不足，可以基于实体类型和名称进行合理推断

请直接返回描述内容，不要包含其他格式标记。
"""
        
        messages = [
            {"role": "system", "content": "你是一个专业的百科全书编辑，擅长撰写准确、详细、结构化的实体描述。"},
            {"role": "user", "content": prompt}
        ]
        
        try:
            description = await self._call_doubao_llm(messages)
            return description.strip()
        except Exception as e:
            # 如果生成失败，返回基础描述
            return entity.description or f"{entity.name}是一个{entity.type}类型的实体。"
    
    async def generate_wiki_content(self, kg: KnowledgeGraph) -> Dict[str, Any]:
        """生成Wiki页面内容"""
        # 生成概述
        overview = await self._generate_overview(kg)
        
        # 为每个实体生成详细描述
        entity_descriptions = {}
        if settings.WIKI_PARALLEL_PROCESSING:
            # 并行生成实体描述
            tasks = []
            for entity in kg.entities:
                task = self._generate_entity_description_with_context(entity, kg)
                tasks.append(task)
            
            descriptions = await asyncio.gather(*tasks, return_exceptions=True)
            
            for entity, description in zip(kg.entities, descriptions):
                if isinstance(description, Exception):
                    entity_descriptions[entity.name] = entity.description or f"{entity.name}的描述"
                else:
                    entity_descriptions[entity.name] = description
        else:
            # 串行生成
            for entity in kg.entities:
                description = await self._generate_entity_description_with_context(entity, kg)
                entity_descriptions[entity.name] = description
        
        # 生成关系网络描述
        network_description = await self._generate_network_description(kg)
        
        # 统计信息
        stats = self._generate_statistics(kg)
        
        return {
            "kg_id": kg.id,
            "kg_name": kg.name,
            "overview": overview,
            "entity_descriptions": entity_descriptions,
            "network_description": network_description,
            "statistics": stats,
            "entities": [entity.model_dump() for entity in kg.entities],
            "relations": [relation.model_dump() for relation in kg.relations],
            "metadata": kg.metadata,
            "generated_at": datetime.now().isoformat(),
            "last_updated": kg.updated_at.isoformat()
        }
    
    async def _generate_entity_description_with_context(self, entity: Entity, kg: KnowledgeGraph) -> str:
        """为实体生成带上下文的描述"""
        # 找到相关实体和关系
        related_relations = [r for r in kg.relations if r.source == entity.name or r.target == entity.name]
        related_entity_names = set()
        
        for relation in related_relations:
            if relation.source == entity.name:
                related_entity_names.add(relation.target)
            else:
                related_entity_names.add(relation.source)
        
        related_entities = [e for e in kg.entities if e.name in related_entity_names]
        
        return await self.generate_entity_description(entity, related_entities, related_relations)
    
    async def _generate_overview(self, kg: KnowledgeGraph) -> str:
        """生成知识图谱概述"""
        entity_types = {}
        for entity in kg.entities:
            entity_types[entity.type] = entity_types.get(entity.type, 0) + 1
        
        relation_types = {}
        for relation in kg.relations:
            relation_types[relation.relation] = relation_types.get(relation.relation, 0) + 1
        
        prompt = f"""
请为以下知识图谱生成一个概述性的介绍。

知识图谱信息：
- 名称：{kg.name}
- 实体总数：{len(kg.entities)}
- 关系总数：{len(kg.relations)}
- 创建时间：{kg.created_at.strftime('%Y年%m月%d日')}

实体类型分布：
{chr(10).join([f"- {t}: {c}个" for t, c in entity_types.items()])}

关系类型分布：
{chr(10).join([f"- {t}: {c}个" for t, c in relation_types.items()])}

主要实体（前10个）：
{chr(10).join([f"- {e.name} ({e.type})" for e in kg.entities[:10]])}

要求：
1. 生成一个200-400字的概述
2. 介绍这个知识图谱的主要内容和特点
3. 提及主要的实体类型和关系类型
4. 使用客观、专业的语调
5. 突出知识图谱的价值和应用场景

请直接返回概述内容。
"""
        
        messages = [
            {"role": "system", "content": "你是一个专业的知识图谱分析师，擅长总结和介绍复杂的知识结构。"},
            {"role": "user", "content": prompt}
        ]
        
        try:
            overview = await self._call_doubao_llm(messages)
            return overview.strip()
        except Exception as e:
            return f"这是一个包含{len(kg.entities)}个实体和{len(kg.relations)}个关系的知识图谱，涵盖了{', '.join(entity_types.keys())}等多个领域。"
    
    async def _generate_network_description(self, kg: KnowledgeGraph) -> str:
        """生成关系网络描述"""
        # 分析网络结构
        entity_degrees = {}
        for relation in kg.relations:
            entity_degrees[relation.source] = entity_degrees.get(relation.source, 0) + 1
            entity_degrees[relation.target] = entity_degrees.get(relation.target, 0) + 1
        
        # 找到核心实体（度数最高的实体）
        core_entities = sorted(entity_degrees.items(), key=lambda x: x[1], reverse=True)[:5]
        
        prompt = f"""
请描述这个知识图谱的关系网络结构特点。

网络统计：
- 总实体数：{len(kg.entities)}
- 总关系数：{len(kg.relations)}
- 平均度数：{len(kg.relations) * 2 / len(kg.entities) if kg.entities else 0:.2f}

核心实体（按连接数排序）：
{chr(10).join([f"- {name}: {degree}个连接" for name, degree in core_entities])}

主要关系类型：
{chr(10).join([f"- {r.relation}" for r in kg.relations[:10]])}

要求：
1. 分析网络的连接特点和结构
2. 描述核心实体的作用
3. 说明主要的关系模式
4. 100-200字的简洁描述

请直接返回描述内容。
"""
        
        messages = [
            {"role": "system", "content": "你是一个网络分析专家，擅长分析和描述复杂网络的结构特征。"},
            {"role": "user", "content": prompt}
        ]
        
        try:
            description = await self._call_doubao_llm(messages)
            return description.strip()
        except Exception as e:
            return f"该知识图谱形成了一个包含{len(kg.entities)}个节点和{len(kg.relations)}个边的复杂网络结构。"
    
    def _generate_statistics(self, kg: KnowledgeGraph) -> Dict[str, Any]:
        """生成统计信息"""
        entity_types = {}
        for entity in kg.entities:
            entity_types[entity.type] = entity_types.get(entity.type, 0) + 1
        
        relation_types = {}
        for relation in kg.relations:
            relation_types[relation.relation] = relation_types.get(relation.relation, 0) + 1
        
        # 计算网络度数
        entity_degrees = {}
        for relation in kg.relations:
            entity_degrees[relation.source] = entity_degrees.get(relation.source, 0) + 1
            entity_degrees[relation.target] = entity_degrees.get(relation.target, 0) + 1
        
        avg_degree = sum(entity_degrees.values()) / len(entity_degrees) if entity_degrees else 0
        max_degree = max(entity_degrees.values()) if entity_degrees else 0
        
        return {
            "total_entities": len(kg.entities),
            "total_relations": len(kg.relations),
            "entity_types": entity_types,
            "relation_types": relation_types,
            "average_degree": round(avg_degree, 2),
            "max_degree": max_degree,
            "density": round(len(kg.relations) / (len(kg.entities) * (len(kg.entities) - 1) / 2), 4) if len(kg.entities) > 1 else 0
        }

    def _create_default_template(self) -> str:
        """创建默认的Wiki模板"""
        return """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ kg_name }} - 知识图谱Wiki</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .header .subtitle {
            margin-top: 10px;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .content {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .section h3 {
            color: #34495e;
            margin-top: 25px;
            margin-bottom: 15px;
        }
        .entity-card {
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .entity-card h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
            font-size: 1.3em;
        }
        .entity-type {
            background: #e74c3c;
            color: white;
            padding: 3px 8px;
            border-radius: 15px;
            font-size: 0.8em;
            margin-left: 10px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }
        .stat-label {
            color: #7f8c8d;
            margin-top: 5px;
        }
        .toc {
            background: #f1f2f6;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .toc h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 8px 0;
        }
        .toc a {
            color: #3498db;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        .toc a:hover {
            background-color: #3498db;
            color: white;
        }
        .timestamp {
            color: #7f8c8d;
            font-size: 0.9em;
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
        }
        .network-viz {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .back-to-graph {
            background: #28a745;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            display: inline-block;
            margin: 10px 0;
            transition: background-color 0.3s;
        }
        .back-to-graph:hover {
            background: #218838;
            color: white;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ kg_name }}</h1>
        <div class="subtitle">知识图谱 Wiki 页面</div>
        <div class="subtitle">生成时间: {{ generated_at }}</div>
    </div>

    <div class="content">
        <div class="toc">
            <h3>📋 目录</h3>
            <ul>
                <li><a href="#overview">概述</a></li>
                <li><a href="#statistics">统计信息</a></li>
                <li><a href="#entities">实体详情</a></li>
                <li><a href="#network">关系网络</a></li>
                <li><a href="#visualization">图谱可视化</a></li>
            </ul>
        </div>

        <div class="section" id="overview">
            <h2>📖 概述</h2>
            <p>{{ overview }}</p>
            <a href="/kg/{{ kg_id }}" class="back-to-graph">🔗 查看交互式知识图谱</a>
        </div>

        <div class="section" id="statistics">
            <h2>📊 统计信息</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">{{ statistics.total_entities }}</div>
                    <div class="stat-label">实体总数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ statistics.total_relations }}</div>
                    <div class="stat-label">关系总数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ statistics.average_degree }}</div>
                    <div class="stat-label">平均度数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ statistics.density }}</div>
                    <div class="stat-label">网络密度</div>
                </div>
            </div>

            <h3>实体类型分布</h3>
            <ul>
            {% for type, count in statistics.entity_types.items() %}
                <li><strong>{{ type }}</strong>: {{ count }} 个</li>
            {% endfor %}
            </ul>

            <h3>关系类型分布</h3>
            <ul>
            {% for type, count in statistics.relation_types.items() %}
                <li><strong>{{ type }}</strong>: {{ count }} 个</li>
            {% endfor %}
            </ul>
        </div>

        <div class="section" id="entities">
            <h2>🏷️ 实体详情</h2>
            {% for entity in entities %}
            <div class="entity-card">
                <h4>
                    {{ entity.name }}
                    <span class="entity-type">{{ entity.type }}</span>
                </h4>
                <p>{{ entity_descriptions[entity.name] }}</p>
            </div>
            {% endfor %}
        </div>

        <div class="section" id="network">
            <h2>🕸️ 关系网络</h2>
            <p>{{ network_description }}</p>

            <h3>主要关系</h3>
            <ul>
            {% for relation in relations[:20] %}
                <li><strong>{{ relation.source }}</strong> --{{ relation.relation }}--> <strong>{{ relation.target }}</strong>
                {% if relation.description %}
                    <br><small style="color: #666;">{{ relation.description }}</small>
                {% endif %}
                </li>
            {% endfor %}
            {% if relations|length > 20 %}
                <li><em>... 还有 {{ relations|length - 20 }} 个关系</em></li>
            {% endif %}
            </ul>
        </div>

        <div class="section" id="visualization">
            <h2>🎨 图谱可视化</h2>
            <div class="network-viz">
                <div>
                    <h3>交互式图谱可视化</h3>
                    <p>点击下方按钮查看完整的交互式知识图谱</p>
                    <a href="/kg/{{ kg_id }}" class="back-to-graph">🚀 打开图谱可视化</a>
                </div>
            </div>
        </div>
    </div>

    <div class="timestamp">
        <p>📅 最后更新: {{ last_updated }} | 🤖 由AI自动生成</p>
        <p>💡 这是一个动态Wiki页面，会随着知识图谱的更新而自动更新</p>
    </div>

    <script>
        // 平滑滚动到锚点
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>"""

    async def generate_wiki_page(self, kg: KnowledgeGraph) -> str:
        """生成完整的Wiki页面HTML"""
        # 生成Wiki内容
        wiki_content = await self.generate_wiki_content(kg)

        # 确保模板目录存在
        self.template_dir.mkdir(exist_ok=True)

        # 创建默认模板（如果不存在）
        template_path = self.template_dir / "wiki_template.html"
        if not template_path.exists():
            with open(template_path, 'w', encoding='utf-8') as f:
                f.write(self._create_default_template())

        # 渲染模板
        try:
            template = self.jinja_env.get_template("wiki_template.html")
        except:
            # 如果模板加载失败，使用字符串模板
            template = Template(self._create_default_template())

        # 格式化时间
        generated_at = datetime.fromisoformat(wiki_content["generated_at"]).strftime("%Y年%m月%d日 %H:%M")
        last_updated = datetime.fromisoformat(wiki_content["last_updated"]).strftime("%Y年%m月%d日 %H:%M")

        html_content = template.render(
            kg_id=wiki_content["kg_id"],
            kg_name=wiki_content["kg_name"],
            overview=wiki_content["overview"],
            entity_descriptions=wiki_content["entity_descriptions"],
            network_description=wiki_content["network_description"],
            statistics=wiki_content["statistics"],
            entities=wiki_content["entities"],
            relations=wiki_content["relations"],
            generated_at=generated_at,
            last_updated=last_updated
        )

        return html_content

    async def save_wiki_page(self, kg: KnowledgeGraph) -> str:
        """保存Wiki页面到文件"""
        html_content = await self.generate_wiki_page(kg)

        # 保存HTML文件
        wiki_file_path = self.wiki_dir / f"{kg.id}.html"
        with open(wiki_file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        # 保存JSON数据（用于API访问）
        wiki_content = await self.generate_wiki_content(kg)
        json_file_path = self.wiki_dir / f"{kg.id}.json"
        with open(json_file_path, 'w', encoding='utf-8') as f:
            json.dump(wiki_content, f, ensure_ascii=False, indent=2)

        return str(wiki_file_path)

    async def get_wiki_url(self, kg_id: str) -> Optional[str]:
        """获取Wiki页面URL"""
        wiki_file_path = self.wiki_dir / f"{kg_id}.html"
        if wiki_file_path.exists():
            return f"/wiki/{kg_id}"
        return None

    async def update_wiki_page(self, kg: KnowledgeGraph) -> str:
        """更新Wiki页面"""
        return await self.save_wiki_page(kg)

    async def delete_wiki_page(self, kg_id: str) -> bool:
        """删除Wiki页面"""
        try:
            html_file = self.wiki_dir / f"{kg_id}.html"
            json_file = self.wiki_dir / f"{kg_id}.json"

            if html_file.exists():
                html_file.unlink()
            if json_file.exists():
                json_file.unlink()

            return True
        except Exception as e:
            return False

    async def list_wiki_pages(self) -> List[Dict[str, Any]]:
        """列出所有Wiki页面"""
        wiki_pages = []

        for json_file in self.wiki_dir.glob("*.json"):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    wiki_data = json.load(f)

                wiki_pages.append({
                    "kg_id": wiki_data["kg_id"],
                    "kg_name": wiki_data["kg_name"],
                    "url": f"/wiki/{wiki_data['kg_id']}",
                    "generated_at": wiki_data["generated_at"],
                    "last_updated": wiki_data["last_updated"],
                    "entity_count": wiki_data["statistics"]["total_entities"],
                    "relation_count": wiki_data["statistics"]["total_relations"]
                })
            except Exception as e:
                continue

        # 按更新时间排序
        wiki_pages.sort(key=lambda x: x["last_updated"], reverse=True)

        return wiki_pages
