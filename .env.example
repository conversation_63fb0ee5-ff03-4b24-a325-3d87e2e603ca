# ==================== 应用基本配置 ====================
APP_NAME=知识图谱构建系统
VERSION=1.0.0
DEBUG=true
HOST=0.0.0.0
PORT=8000

# ==================== 前端配置 ====================
FRONTEND_HOST=localhost
FRONTEND_PORT=3000
FRONTEND_BUILD_DIR=frontend/dist

# ==================== LLM API配置 ====================
# LLM API基础URL
LLM_API_BASE=https://gateway.chat.sensedeal.vip/v1
# LLM API密钥
LLM_API_KEY=your-api-key-here
# 使用的模型名称
LLM_MODEL=qwen2.5-32b-instruct-int4
# 最大token数
LLM_MAX_TOKENS=4000
# 温度参数（0-1，控制生成的随机性）
LLM_TEMPERATURE=0.1

# ==================== 豆包模型配置（用于Wiki生成） ====================
# 豆包模型名称
DOUBAO_MODEL=doubao-seed-1.6
# 豆包模型最大token数
DOUBAO_MAX_TOKENS=8000
# 豆包模型温度参数
DOUBAO_TEMPERATURE=0.3

# ==================== Wiki生成配置 ====================
# Wiki生成使用的模型
WIKI_GENERATION_MODEL=doubao-seed-1.6
# Wiki页面输出目录
WIKI_OUTPUT_DIR=data/wiki_pages
# Wiki模板目录
WIKI_TEMPLATE_DIR=templates/wiki
# 是否启用Wiki生成
ENABLE_WIKI_GENERATION=true
# 是否自动更新Wiki
WIKI_AUTO_UPDATE=true
# 是否启用Wiki并行处理
WIKI_PARALLEL_PROCESSING=true
# Wiki生成最大并发数
WIKI_MAX_CONCURRENT=5

# ==================== 文件处理配置 ====================
# 文件上传目录
UPLOAD_DIR=data/uploads
# 最大文件大小（字节）
MAX_FILE_SIZE=104857600
# 允许的文件扩展名（逗号分隔）
ALLOWED_EXTENSIONS=.txt,.pdf,.docx,.doc,.xlsx,.xls,.csv,.json

# ==================== 知识图谱配置 ====================
# 知识图谱数据目录
KG_DATA_DIR=data/knowledge_graph
# 每个文本块最大实体数
MAX_ENTITIES_PER_CHUNK=50
# 每个文本块最大关系数
MAX_RELATIONS_PER_CHUNK=100
# 文本块大小
CHUNK_SIZE=3000
# 文本块重叠大小
CHUNK_OVERLAP=300
# 最大处理文本块数量
MAX_CHUNKS_TO_PROCESS=50

# ==================== 并发处理配置 ====================
# 最大并发处理的文本块数量
MAX_CONCURRENT_CHUNKS=8
# 批处理大小
BATCH_SIZE=4
# LLM请求超时时间（秒）
LLM_REQUEST_TIMEOUT=120
# LLM请求最大重试次数
LLM_MAX_RETRIES=3
# 重试延迟（秒）
LLM_RETRY_DELAY=1.0
# HTTP连接池大小
CONNECTION_POOL_SIZE=20
# 是否启用并行处理
ENABLE_PARALLEL_PROCESSING=true

# ==================== 文本预处理配置 ====================
# 预处理分段大小
PREPROCESS_SEGMENT_SIZE=6000
# 预处理分段重叠大小
PREPROCESS_OVERLAP_SIZE=800
# 最大预处理分段数量
MAX_PREPROCESS_SEGMENTS=10
# 启用增强预处理
ENABLE_ENHANCED_PREPROCESSING=true
# 关注关系提取优化
RELATION_EXTRACTION_FOCUS=true

# ==================== 实体处理配置 ====================
# 启用实体消歧
ENABLE_ENTITY_DISAMBIGUATION=true
# 启用LLM实体消歧
ENABLE_LLM_ENTITY_DISAMBIGUATION=true
# 实体相似度阈值
ENTITY_SIMILARITY_THRESHOLD=0.8
# LLM实体变体识别置信度阈值
LLM_ENTITY_CONFIDENCE_THRESHOLD=0.7
# 启用关系推理
ENABLE_RELATION_INFERENCE=true
# 启用连通性检查
ENABLE_CONNECTIVITY_CHECK=true
# 每个实体最大变体数量
MAX_ENTITY_VARIANTS=5
# 模糊匹配阈值
FUZZY_MATCH_THRESHOLD=0.7

# ==================== 实体标准化配置 ====================
# 启用实体标准化
ENABLE_ENTITY_STANDARDIZATION=true
# 实体完整性阈值
ENTITY_COMPLETION_THRESHOLD=0.85
# 启用实体补全
ENABLE_ENTITY_COMPLETION=true
# 临时文件格式
ENTITY_TEMP_FILE_FORMAT=structured
# 最小实体名称长度
MIN_ENTITY_NAME_LENGTH=2
# 最大实体名称长度
MAX_ENTITY_NAME_LENGTH=50
# 启用实体验证
ENABLE_ENTITY_VALIDATION=true

# ==================== 日志配置 ====================
# 日志级别
LOG_LEVEL=INFO
# 日志文件路径
LOG_FILE=logs/app.log

# ==================== 数据库配置 ====================
# 数据库URL（可选）
# DATABASE_URL=sqlite:///./data/app.db
# Redis URL（可选）
# REDIS_URL=redis://localhost:6379

# ==================== 安全配置 ====================
# 密钥（用于JWT等）
SECRET_KEY=your-secret-key-change-this-in-production
# CORS允许的源（逗号分隔，*表示允许所有）
CORS_ORIGINS=*

# ==================== 开发环境配置 ====================
# 是否在开发模式下运行
DEVELOPMENT_MODE=true
# 是否启用热重载
ENABLE_HOT_RELOAD=true
# 是否显示详细错误信息
SHOW_DETAILED_ERRORS=true

# ==================== 生产环境配置 ====================
# 生产环境中请修改以下配置：
# DEBUG=false
# CORS_ORIGINS=https://yourdomain.com
# SECRET_KEY=your-production-secret-key
# LLM_API_KEY=your-production-api-key
