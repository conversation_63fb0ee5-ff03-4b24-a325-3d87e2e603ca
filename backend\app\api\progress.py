"""
进度跟踪API路由
"""
from typing import Dict, List
from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse

from app.services.progress_tracker import progress_tracker, ProgressInfo, TaskStatus
from loguru import logger

router = APIRouter()


@router.get("/task/{task_id}")
async def get_task_progress(task_id: str):
    """获取指定任务的进度"""
    try:
        progress = progress_tracker.get_progress(task_id)
        if not progress:
            raise HTTPException(status_code=404, detail=f"任务未找到: {task_id}")
        
        return {
            "task_id": progress.task_id,
            "task_name": progress.task_name,
            "status": progress.status.value,
            "current": progress.current,
            "total": progress.total,
            "percentage": progress.percentage,
            "elapsed_time": progress.elapsed_time,
            "estimated_remaining": progress.estimated_remaining,
            "error_message": progress.error_message
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取进度失败: {str(e)}")


@router.get("/tasks")
async def get_all_tasks():
    """获取所有任务的进度"""
    try:
        tasks = progress_tracker.get_all_tasks()
        result = []
        
        for task_id, progress in tasks.items():
            result.append({
                "task_id": progress.task_id,
                "task_name": progress.task_name,
                "status": progress.status.value,
                "current": progress.current,
                "total": progress.total,
                "percentage": progress.percentage,
                "elapsed_time": progress.elapsed_time,
                "estimated_remaining": progress.estimated_remaining,
                "error_message": progress.error_message
            })
        
        return {"tasks": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")


@router.post("/task/{task_id}/cancel")
async def cancel_task(task_id: str):
    """取消指定任务"""
    try:
        progress = progress_tracker.get_progress(task_id)
        if not progress:
            raise HTTPException(status_code=404, detail=f"任务未找到: {task_id}")
        
        if progress.status not in [TaskStatus.PENDING, TaskStatus.RUNNING]:
            raise HTTPException(status_code=400, detail="任务无法取消")
        
        await progress_tracker.cancel_task(task_id)
        
        return {
            "success": True,
            "message": f"任务 {task_id} 已取消"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")


@router.delete("/tasks/cleanup")
async def cleanup_completed_tasks(max_age_hours: int = 24):
    """清理已完成的旧任务"""
    try:
        await progress_tracker.cleanup_completed_tasks(max_age_hours)
        
        return {
            "success": True,
            "message": f"已清理超过 {max_age_hours} 小时的已完成任务"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清理任务失败: {str(e)}")


@router.get("/stats")
async def get_progress_stats():
    """获取进度统计信息"""
    try:
        tasks = progress_tracker.get_all_tasks()
        
        stats = {
            "total_tasks": len(tasks),
            "pending": 0,
            "running": 0,
            "completed": 0,
            "failed": 0,
            "cancelled": 0
        }
        
        for progress in tasks.values():
            if progress.status == TaskStatus.PENDING:
                stats["pending"] += 1
            elif progress.status == TaskStatus.RUNNING:
                stats["running"] += 1
            elif progress.status == TaskStatus.COMPLETED:
                stats["completed"] += 1
            elif progress.status == TaskStatus.FAILED:
                stats["failed"] += 1
            elif progress.status == TaskStatus.CANCELLED:
                stats["cancelled"] += 1
        
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")
