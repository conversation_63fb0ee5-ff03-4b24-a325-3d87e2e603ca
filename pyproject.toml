[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "knowledge-graph-system"
version = "1.0.0"
description = "基于大语言模型的智能知识图谱构建和可视化系统"
authors = [
    {name = "Knowledge Graph Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.8"
keywords = ["knowledge-graph", "llm", "nlp", "fastapi", "react"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
]

dependencies = [
    # Web框架
    "fastapi>=0.104.1",
    "uvicorn>=0.24.0",
    "python-multipart>=0.0.6",
    
    # HTTP客户端
    "httpx>=0.25.2",
    "requests>=2.31.0",
    
    # 数据处理
    "pandas>=2.1.3",
    "numpy>=1.24.3",
    
    # 文件处理
    "PyPDF2>=3.0.1",
    "python-docx>=1.1.0",
    "openpyxl>=3.1.2",
    "aiofiles>=23.0.0",
    
    # 图数据结构
    "networkx>=3.2.1",
    
    # 自然语言处理
    "jieba>=0.42.1",
    "nltk>=3.8.1",
    
    # 数据验证
    "pydantic>=2.5.0",
    "pydantic-settings>=2.0.0",
    
    # 配置管理
    "python-dotenv>=1.0.0",
    
    # 日志
    "loguru>=0.7.2",
    
    # JSON处理
    "orjson>=3.9.10",

    # 模板引擎
    "jinja2>=3.1.2",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]

test = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "httpx>=0.25.2",
]

docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.0.0",
    "mkdocstrings[python]>=0.22.0",
]

[project.urls]
Homepage = "https://github.com/your-org/knowledge-graph-system"
Documentation = "https://your-org.github.io/knowledge-graph-system"
Repository = "https://github.com/your-org/knowledge-graph-system"
Issues = "https://github.com/your-org/knowledge-graph-system/issues"

[project.scripts]
kg-server = "app.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["app*", "utils*"]
exclude = ["tests*", "frontend*", "backend*", "docs*"]

[tool.setuptools.package-data]
"*" = ["*.json", "*.yaml", "*.yml", "*.toml"]

# ==================== 开发工具配置 ====================

[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | frontend
  | node_modules
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app", "utils"]
skip_glob = ["frontend/*", "node_modules/*"]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".venv",
    "frontend",
    "node_modules"
]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "jieba.*",
    "nltk.*",
    "PyPDF2.*",
    "docx.*",
    "networkx.*",
    "loguru.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["app", "utils"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/frontend/*",
    "*/node_modules/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
