# 知识图谱 Wiki 功能使用指南

## 🚀 快速开始

### 1. 启动系统
```bash
# 启动后端服务（自动打开前端）
python -m app.main

# 或使用启动脚本
start_backend.bat
```

### 2. 构建知识图谱（自动生成Wiki）
1. 访问前端界面：http://localhost:3000
2. 上传文档文件（支持 PDF、DOCX、TXT 等）
3. 点击"处理文件"开始构建知识图谱
4. 系统自动生成知识图谱和对应的 Wiki 页面

### 3. 查看 Wiki 页面
- **方式1**: 在知识图谱查看界面点击"Wiki页面"链接
- **方式2**: 直接访问 `http://localhost:8000/wiki/{图谱ID}.html`
- **方式3**: 通过 API 获取 Wiki 信息

## 📖 Wiki 页面功能

### 页面内容
1. **📋 目录导航**: 快速跳转到各个章节
2. **📖 概述**: AI生成的图谱整体介绍
3. **📊 统计信息**: 实体数量、关系数量、网络密度等
4. **🏷️ 实体详情**: 每个实体的详细描述（AI生成）
5. **🕸️ 关系网络**: 实体间关系的分析和描述
6. **🎨 图谱可视化**: 跳转到交互式图谱界面

### 交互功能
- **平滑滚动**: 点击目录项平滑滚动到对应章节
- **返回图谱**: 一键返回交互式知识图谱界面
- **响应式设计**: 适配手机、平板、电脑等设备

## 🔧 API 使用

### 1. 生成 Wiki 页面
```bash
curl -X POST "http://localhost:8000/api/wiki/generate" \
     -H "Content-Type: application/json" \
     -d '{
       "kg_id": "your-knowledge-graph-id",
       "force_regenerate": false
     }'
```

**响应示例**:
```json
{
  "success": true,
  "message": "Wiki页面生成成功",
  "wiki_url": "/wiki/your-kg-id.html",
  "generation_time": 15.2,
  "entity_count": 25
}
```

### 2. 获取 Wiki 页面列表
```bash
curl -X GET "http://localhost:8000/api/wiki/list"
```

**响应示例**:
```json
{
  "success": true,
  "wiki_pages": [
    {
      "kg_id": "kg-123",
      "kg_name": "人工智能知识图谱",
      "url": "/wiki/kg-123.html",
      "generated_at": "2024-01-01T10:00:00",
      "last_updated": "2024-01-01T10:00:00",
      "entity_count": 50,
      "relation_count": 120
    }
  ]
}
```

### 3. 获取 Wiki 页面信息
```bash
curl -X GET "http://localhost:8000/api/wiki/{kg_id}"
```

### 4. 获取 Wiki 内容（JSON格式）
```bash
curl -X GET "http://localhost:8000/api/wiki/{kg_id}/content"
```

### 5. 更新 Wiki 页面
```bash
curl -X PUT "http://localhost:8000/api/wiki/{kg_id}/update"
```

### 6. 删除 Wiki 页面
```bash
curl -X DELETE "http://localhost:8000/api/wiki/{kg_id}"
```

## ⚙️ 配置选项

### 环境变量配置（.env文件）
```env
# 启用Wiki生成
ENABLE_WIKI_GENERATION=true

# 豆包模型配置（用于Wiki生成）
DOUBAO_MODEL=doubao-seed-1.6
DOUBAO_MAX_TOKENS=8000
DOUBAO_TEMPERATURE=0.3

# Wiki生成配置
WIKI_GENERATION_MODEL=doubao-seed-1.6
WIKI_OUTPUT_DIR=data/wiki_pages
WIKI_TEMPLATE_DIR=templates/wiki
WIKI_AUTO_UPDATE=true
WIKI_PARALLEL_PROCESSING=true
WIKI_MAX_CONCURRENT=5

# 主模型配置（已优化）
LLM_MODEL=qwen2.5-32b-instruct-int4
```

### 性能调优
- **并行处理**: `WIKI_PARALLEL_PROCESSING=true` 启用并行生成
- **并发数量**: `WIKI_MAX_CONCURRENT=5` 控制同时处理的实体数
- **自动更新**: `WIKI_AUTO_UPDATE=true` 图谱更新时自动更新Wiki

## 🎯 使用场景

### 1. 学术研究
- 上传研究论文，生成学术概念知识图谱
- 通过Wiki页面深入了解概念定义和关系
- 导出Wiki内容用于研究报告

### 2. 企业知识管理
- 处理企业文档，构建组织知识图谱
- Wiki页面作为知识库供员工查阅
- 实时更新保持知识的时效性

### 3. 教育培训
- 教材内容转化为知识图谱
- Wiki页面提供结构化的学习材料
- 可视化图谱辅助理解概念关系

### 4. 内容创作
- 分析文本材料，提取关键信息
- Wiki页面整理成结构化内容
- 为写作提供素材和灵感

## 🔍 故障排除

### 常见问题

1. **Wiki页面无法生成**
   - 检查 `ENABLE_WIKI_GENERATION=true`
   - 确认豆包模型API可用
   - 查看日志文件获取详细错误信息

2. **生成速度慢**
   - 启用并行处理：`WIKI_PARALLEL_PROCESSING=true`
   - 调整并发数：`WIKI_MAX_CONCURRENT=5`
   - 检查网络连接和API响应时间

3. **Wiki内容质量不佳**
   - 调整温度参数：`DOUBAO_TEMPERATURE=0.3`
   - 增加最大token数：`DOUBAO_MAX_TOKENS=8000`
   - 确保输入文本质量良好

4. **页面样式异常**
   - 检查模板文件是否完整
   - 清除浏览器缓存
   - 确认静态文件服务正常

### 调试方法
```bash
# 查看应用日志
tail -f logs/app.log

# 测试API连接
curl -X GET "http://localhost:8000/health"

# 检查Wiki目录
ls -la data/wiki_pages/

# 验证模型配置
curl -X GET "http://localhost:8000/api/llm/config"
```

## 📈 性能监控

### 生成时间参考
- **小型图谱**（<20个实体）: 5-10秒
- **中型图谱**（20-50个实体）: 10-30秒  
- **大型图谱**（50+个实体）: 30-60秒

### 优化建议
1. **启用并行处理**减少生成时间
2. **合理设置并发数**避免API限流
3. **定期清理**旧的Wiki文件释放空间
4. **监控API使用量**避免超出配额

## 🎉 最佳实践

### 1. 内容质量
- 上传高质量、结构化的文档
- 确保文本内容清晰、逻辑性强
- 避免过于碎片化的信息

### 2. 系统维护
- 定期备份Wiki页面和数据
- 监控磁盘空间使用情况
- 及时更新模型配置和API密钥

### 3. 用户体验
- 为用户提供Wiki页面链接
- 在界面中突出显示Wiki功能
- 收集用户反馈持续改进

通过以上指南，您可以充分利用知识图谱Wiki功能，为用户提供丰富的知识浏览体验！
