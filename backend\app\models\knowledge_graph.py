"""
知识图谱相关数据模型
"""
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
import networkx as nx
from datetime import datetime

class Entity(BaseModel):
    """实体模型"""
    id: str = Field(..., description="实体唯一标识")
    name: str = Field(..., description="实体名称")
    type: str = Field(..., description="实体类型")
    properties: Dict[str, Any] = Field(default_factory=dict, description="实体属性")
    description: Optional[str] = Field(None, description="实体描述")
    source_file: Optional[str] = Field(None, description="来源文件")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")

class Relation(BaseModel):
    """关系模型"""
    id: str = Field(..., description="关系唯一标识")
    source_entity: str = Field(..., description="源实体ID")
    target_entity: str = Field(..., description="目标实体ID")
    relation_type: str = Field(..., description="关系类型")
    properties: Dict[str, Any] = Field(default_factory=dict, description="关系属性")
    confidence: float = Field(default=1.0, description="置信度", ge=0.0, le=1.0)
    source_text: Optional[str] = Field(None, description="关系来源文本")
    source_file: Optional[str] = Field(None, description="来源文件")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")

class KnowledgeGraph(BaseModel):
    """知识图谱模型"""
    id: str = Field(..., description="图谱唯一标识")
    name: str = Field(..., description="图谱名称")
    entities: List[Entity] = Field(default_factory=list, description="实体列表")
    relations: List[Relation] = Field(default_factory=list, description="关系列表")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    
    def to_networkx(self) -> nx.Graph:
        """转换为NetworkX图对象"""
        G = nx.Graph()
        
        # 添加节点
        for entity in self.entities:
            G.add_node(
                entity.id,
                name=entity.name,
                type=entity.type,
                properties=entity.properties,
                description=entity.description
            )
        
        # 添加边
        for relation in self.relations:
            G.add_edge(
                relation.source_entity,
                relation.target_entity,
                relation_type=relation.relation_type,
                properties=relation.properties,
                confidence=relation.confidence,
                source_text=relation.source_text
            )
        
        return G
    
    @classmethod
    def from_networkx(cls, graph: nx.Graph, kg_id: str, name: str) -> "KnowledgeGraph":
        """从NetworkX图对象创建知识图谱"""
        entities = []
        relations = []
        
        # 提取实体
        for node_id, node_data in graph.nodes(data=True):
            entity = Entity(
                id=node_id,
                name=node_data.get("name", node_id),
                type=node_data.get("type", "unknown"),
                properties=node_data.get("properties", {}),
                description=node_data.get("description")
            )
            entities.append(entity)
        
        # 提取关系
        for source, target, edge_data in graph.edges(data=True):
            relation = Relation(
                id=f"{source}_{target}_{edge_data.get('relation_type', 'related')}",
                source_entity=source,
                target_entity=target,
                relation_type=edge_data.get("relation_type", "related"),
                properties=edge_data.get("properties", {}),
                confidence=edge_data.get("confidence", 1.0),
                source_text=edge_data.get("source_text")
            )
            relations.append(relation)
        
        return cls(
            id=kg_id,
            name=name,
            entities=entities,
            relations=relations
        )

    def to_networkx(self):
        """转换为NetworkX图"""
        import networkx as nx

        G = nx.Graph()

        # 添加节点
        for entity in self.entities:
            G.add_node(entity.id,
                      name=entity.name,
                      type=entity.type,
                      description=entity.description,
                      properties=entity.properties)

        # 添加边
        for relation in self.relations:
            G.add_edge(relation.source_entity,
                      relation.target_entity,
                      relation_type=relation.relation_type,
                      confidence=relation.confidence,
                      properties=relation.properties,
                      source_text=relation.source_text)

        return G

class GraphSearchResult(BaseModel):
    """图搜索结果"""
    entities: List[Entity] = Field(default_factory=list, description="匹配的实体")
    relations: List[Relation] = Field(default_factory=list, description="相关关系")
    subgraph: Optional[Dict[str, Any]] = Field(None, description="子图数据")

class GraphStats(BaseModel):
    """图统计信息"""
    total_entities: int = Field(..., description="实体总数")
    total_relations: int = Field(..., description="关系总数")
    entity_types: Dict[str, int] = Field(default_factory=dict, description="实体类型统计")
    relation_types: Dict[str, int] = Field(default_factory=dict, description="关系类型统计")
    avg_degree: float = Field(..., description="平均度数")
    density: float = Field(..., description="图密度")
