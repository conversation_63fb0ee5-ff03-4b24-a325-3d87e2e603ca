
实体标准化测试文档

这个文档专门设计来测试实体名称截断问题的修复效果。

公司信息：
广州团队负责华南地区的业务拓展工作。广州团队由资深的销售经理张三领导。
深圳分公司是我们在南方的重要据点。深圳分公司主要负责技术研发。
北京总部设立了多个部门。北京总部的人力资源部门负责招聘工作。

人员信息：
项目经理李四负责整个开发项目。李四有丰富的项目管理经验。
技术总监王五专注于系统架构设计。王五使用Python和Java技术。
数据科学家赵六研究机器学习算法。赵六发表了多篇学术论文。

产品信息：
知识图谱系统是我们的核心产品。知识图谱系统采用图数据库技术。
智能问答平台正在开发中。智能问答平台将集成自然语言处理功能。
数据分析工具已经上线运营。数据分析工具支持多种数据源。

技术栈：
Python编程语言是主要的开发工具。Python编程语言具有丰富的生态系统。
React前端框架用于用户界面开发。React前端框架提供组件化开发模式。
Neo4j图数据库存储知识图谱数据。Neo4j图数据库支持复杂查询。

地理位置：
中关村科技园区是北京的创新中心。中关村科技园区聚集了众多科技企业。
深圳南山区是科技公司的聚集地。深圳南山区有良好的创业环境。
广州天河区是商业中心。广州天河区交通便利。

项目信息：
人工智能项目是公司的重点投资方向。人工智能项目预计明年完成。
大数据平台建设正在进行中。大数据平台建设需要大量资源投入。
云计算服务迁移计划已经启动。云计算服务迁移计划分三个阶段执行。

这个测试文档故意重复使用相同的实体名称，以测试系统是否能够：
1. 正确识别完整的实体名称（如"广州团队"而不是"广州团"）
2. 合并重复出现的相同实体
3. 建立实体之间的正确关系
4. 避免因文本分块而导致的实体截断问题

测试重点：
- 广州团队 vs 广州团
- 深圳分公司 vs 深圳分
- 北京总部 vs 北京总
- 知识图谱系统 vs 知识图谱
- Python编程语言 vs Python编程
- React前端框架 vs React前端
- Neo4j图数据库 vs Neo4j图数据
- 中关村科技园区 vs 中关村科技园
- 人工智能项目 vs 人工智能
- 大数据平台建设 vs 大数据平台

期望结果：
系统应该能够识别并保留完整的实体名称，避免截断，并正确建立实体间的关系。
