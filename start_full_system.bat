@echo off
echo ========================================
echo 启动完整的知识图谱构建系统
echo ========================================
echo.

:: 设置窗口标题
title 知识图谱系统启动器

:: 检查Python环境
echo [1/4] 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH
    echo 请先安装Python: https://www.python.org/
    pause
    exit /b 1
)
echo ✅ Python环境正常

:: 检查Node.js环境
echo [2/4] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js未安装或未添加到PATH
    echo 请先安装Node.js: https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js环境正常

:: 安装Python依赖
echo [3/4] 安装Python依赖...
pip install -e . >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ Python依赖安装可能有问题，继续启动...
) else (
    echo ✅ Python依赖安装完成
)

:: 检查前端依赖
echo [4/4] 检查前端依赖...
cd frontend
if not exist "node_modules" (
    echo 📦 安装前端依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 前端依赖安装失败
        cd ..
        pause
        exit /b 1
    )
)
echo ✅ 前端依赖检查完成
cd ..

echo.
echo ========================================
echo 启动服务
echo ========================================

:: 启动前端服务（后台）
echo 🚀 启动前端服务...
start "前端服务" cmd /c "cd frontend && npm run dev"

:: 等待前端启动
echo ⏳ 等待前端服务启动...
timeout /t 5 /nobreak >nul

:: 启动后端服务
echo 🚀 启动后端服务...
echo.
echo ========================================
echo 服务信息
echo ========================================
echo 🌐 前端地址: http://localhost:3000
echo 🔗 后端地址: http://localhost:8000  
echo 📚 API文档: http://localhost:8000/docs
echo ========================================
echo.
echo 💡 提示: 
echo   - 前端服务在单独窗口中运行
echo   - 关闭此窗口将停止后端服务
echo   - 使用 Ctrl+C 可以停止后端服务
echo.

:: 启动后端（前台）
python -m app.main

echo.
echo 👋 后端服务已停止
pause
