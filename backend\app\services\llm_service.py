"""
大语言模型服务
"""
import json
import httpx
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from app.core.config import settings
from app.models.knowledge_graph import Entity, Relation
import re
import uuid
from loguru import logger
import time

class LLMService:
    """大语言模型服务类"""

    def __init__(self):
        self.api_base = settings.LLM_API_BASE
        self.api_key = settings.LLM_API_KEY
        self.model = settings.LLM_MODEL
        self.max_tokens = settings.LLM_MAX_TOKENS
        self.temperature = settings.LLM_TEMPERATURE

        # 并发控制
        self.max_concurrent = settings.MAX_CONCURRENT_CHUNKS
        self.semaphore = asyncio.Semaphore(self.max_concurrent)

        # HTTP客户端配置
        self.client_config = {
            "timeout": httpx.Timeout(settings.LLM_REQUEST_TIMEOUT),
            "limits": httpx.Limits(
                max_connections=settings.CONNECTION_POOL_SIZE,
                max_keepalive_connections=settings.CONNECTION_POOL_SIZE // 2
            )
        }
        self._client = None

    async def get_client(self) -> httpx.AsyncClient:
        """获取HTTP客户端实例（单例模式）"""
        if self._client is None or self._client.is_closed:
            self._client = httpx.AsyncClient(**self.client_config)
        return self._client

    async def close_client(self):
        """关闭HTTP客户端"""
        if self._client and not self._client.is_closed:
            await self._client.aclose()
            self._client = None

    async def _call_llm(self, messages: List[Dict[str, str]]) -> str:
        """调用LLM API（带重试机制）"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "model": self.model,
            "messages": messages,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature
        }

        # 使用信号量控制并发
        async with self.semaphore:
            client = await self.get_client()

            for attempt in range(settings.LLM_MAX_RETRIES):
                try:
                    response = await client.post(
                        f"{self.api_base}/chat/completions",
                        headers=headers,
                        json=payload
                    )
                    response.raise_for_status()
                    result = response.json()
                    return result["choices"][0]["message"]["content"]

                except Exception as e:
                    if attempt < settings.LLM_MAX_RETRIES - 1:
                        logger.warning(f"LLM API调用失败，第{attempt + 1}次重试: {e}")
                        await asyncio.sleep(settings.LLM_RETRY_DELAY * (attempt + 1))
                    else:
                        logger.error(f"LLM API调用失败，已达最大重试次数: {e}")
                        raise

    async def extract_entities_and_relations_batch(
        self,
        texts: List[str],
        progress_callback=None
    ) -> List[Tuple[List[Entity], List[Relation]]]:
        """批量并发提取实体和关系"""
        if not settings.ENABLE_PARALLEL_PROCESSING:
            # 如果禁用并行处理，回退到串行处理
            results = []
            for i, text in enumerate(texts):
                result = await self.extract_entities_and_relations(text)
                results.append(result)
                if progress_callback:
                    progress_callback(i + 1, len(texts))
            return results

        # 并发处理
        logger.info(f"开始并发处理 {len(texts)} 个文本块，最大并发数: {self.max_concurrent}")

        async def process_with_progress(i: int, text: str):
            """带进度回调的处理函数"""
            try:
                result = await self.extract_entities_and_relations(text)
                if progress_callback:
                    progress_callback(i + 1, len(texts))
                return result
            except Exception as e:
                logger.error(f"处理文本块 {i} 失败: {e}")
                if progress_callback:
                    progress_callback(i + 1, len(texts))
                return [], []  # 返回空结果而不是抛出异常

        # 创建任务列表
        tasks = [
            process_with_progress(i, text)
            for i, text in enumerate(texts)
        ]

        # 并发执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"文本块 {i} 处理异常: {result}")
                processed_results.append(([], []))  # 空结果
            else:
                processed_results.append(result)

        logger.info(f"批量处理完成，成功处理 {len([r for r in processed_results if r[0] or r[1]])} 个文本块")
        return processed_results

    async def extract_entities_and_relations(self, text: str) -> Tuple[List[Entity], List[Relation]]:
        """从文本中提取实体和关系（增强版）"""

        if settings.RELATION_EXTRACTION_FOCUS:
            return await self._enhanced_extract_entities_and_relations(text)
        else:
            return await self._basic_extract_entities_and_relations(text)

    async def _basic_extract_entities_and_relations(self, text: str) -> Tuple[List[Entity], List[Relation]]:
        """基础实体关系提取（原有逻辑）"""
        # 构建提示词
        prompt = f"""
请从以下文本中提取实体和关系，并严格按照JSON格式返回。

要求：
1. 提取重要的实体（人物、地点、组织、概念、事件等）
2. 识别实体之间的关系
3. 必须返回有效的JSON格式，不要包含任何其他文字
4. 如果没有找到实体或关系，返回空数组

返回格式（只返回JSON，不要其他内容）：
{{
    "entities": [
        {{
            "name": "实体名称",
            "type": "实体类型",
            "description": "实体描述"
        }}
    ],
    "relations": [
        {{
            "source": "源实体名称",
            "target": "目标实体名称",
            "relation": "关系类型",
            "description": "关系描述"
        }}
    ]
}}

文本内容：
{text[:1500]}
"""

        return await self._process_extraction_prompt(prompt, text)

    async def _enhanced_extract_entities_and_relations(self, text: str) -> Tuple[List[Entity], List[Relation]]:
        """增强实体关系提取"""
        # 使用更长的文本和更详细的提示词
        prompt = f"""
请深度分析以下文本，提取所有重要的实体和关系。特别关注关系的识别和分类。

## 实体提取要求：
1. **人物**：姓名、职位、角色等
2. **组织**：公司、机构、团队等
3. **地点**：城市、地区、具体地址等
4. **概念**：技术、理论、方法等
5. **事件**：项目、活动、事件等
6. **产品**：软件、硬件、服务等
7. **时间**：日期、时期、阶段等

## 关系提取要求：
请识别以下类型的关系，并尽可能多地提取：
1. **工作关系**：雇佣、管理、合作、负责等
2. **地理关系**：位于、来自、在...工作等
3. **从属关系**：属于、包含、隶属等
4. **技能关系**：擅长、使用、开发等
5. **项目关系**：参与、负责、开发等
6. **时间关系**：发生在、持续、开始等
7. **因果关系**：导致、影响、促进等
8. **社交关系**：认识、朋友、同事等

## 输出要求：
- 必须返回有效的JSON格式
- 关系数量应该尽可能多，不要遗漏
- 每个关系都要有清晰的描述
- 实体描述要详细具体

返回格式（只返回JSON）：
{{
    "entities": [
        {{
            "name": "实体名称",
            "type": "实体类型",
            "description": "详细描述，包括背景信息"
        }}
    ],
    "relations": [
        {{
            "source": "源实体名称",
            "target": "目标实体名称",
            "relation": "具体关系类型",
            "description": "关系的详细描述和上下文"
        }}
    ]
}}

文本内容：
{text[:3000]}
"""

        return await self._process_extraction_prompt(prompt, text)

    async def _process_extraction_prompt(self, prompt: str, text: str) -> Tuple[List[Entity], List[Relation]]:
        """处理实体关系提取提示词"""
        messages = [
            {"role": "system", "content": "你是一个专业的知识图谱构建助手，擅长从文本中提取实体和关系。请严格按照JSON格式返回结果，不要包含任何解释文字。特别注意要尽可能多地提取关系，不要遗漏任何有价值的实体间连接。"},
            {"role": "user", "content": prompt}
        ]
        
        try:
            response = await self._call_llm(messages)

            # 尝试解析JSON响应
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                try:
                    data = json.loads(json_str)
                except json.JSONDecodeError:
                    # 如果JSON解析失败，尝试清理和修复
                    json_str = self._clean_json_response(json_str)
                    try:
                        data = json.loads(json_str)
                    except json.JSONDecodeError:
                        logger.warning(f"无法解析LLM响应JSON: {json_str[:200]}...")
                        return [], []
            else:
                logger.warning("无法从LLM响应中提取JSON")
                return [], []

            # 转换为实体和关系对象
            entities = []
            relations = []

            # 处理实体
            entity_map = {}  # 用于关系映射
            for entity_data in data.get("entities", []):
                entity_id = str(uuid.uuid4())
                entity = Entity(
                    id=entity_id,
                    name=entity_data.get("name", ""),
                    type=entity_data.get("type", "unknown"),
                    description=entity_data.get("description", "")
                )
                entities.append(entity)
                entity_map[entity.name] = entity_id

            # 处理关系
            for relation_data in data.get("relations", []):
                source_name = relation_data.get("source", "")
                target_name = relation_data.get("target", "")

                if source_name in entity_map and target_name in entity_map:
                    relation = Relation(
                        id=str(uuid.uuid4()),
                        source_entity=entity_map[source_name],
                        target_entity=entity_map[target_name],
                        relation_type=relation_data.get("relation", "related"),
                        source_text=text[:500],  # 保存部分源文本
                        properties={
                            "description": relation_data.get("description", "")
                        }
                    )
                    relations.append(relation)
                else:
                    # 记录未匹配的关系，用于调试
                    logger.debug(f"关系未匹配: {source_name} -> {target_name}")

            logger.info(f"提取到 {len(entities)} 个实体和 {len(relations)} 个关系")
            return entities, relations

        except Exception as e:
            logger.error(f"实体关系提取失败: {e}")
            return [], []
    
    async def summarize_text(self, text: str) -> str:
        """文本摘要"""
        prompt = f"""
请对以下文本进行摘要，提取主要内容和关键信息：

{text[:3000]}
"""
        
        messages = [
            {"role": "system", "content": "你是一个专业的文本摘要助手。"},
            {"role": "user", "content": prompt}
        ]
        
        try:
            return await self._call_llm(messages)
        except Exception as e:
            logger.error(f"文本摘要失败: {e}")
            return "摘要生成失败"
    
    async def classify_entity_type(self, entity_name: str, context: str = "") -> str:
        """实体类型分类"""
        prompt = f"""
请判断实体"{entity_name}"的类型。

上下文：{context[:500]}

可能的类型包括：人物、地点、组织、概念、事件、产品、时间等。

请只返回类型名称。
"""

        messages = [
            {"role": "system", "content": "你是一个实体分类专家。"},
            {"role": "user", "content": prompt}
        ]

        try:
            result = await self._call_llm(messages)
            return result.strip()
        except Exception as e:
            logger.error(f"实体分类失败: {e}")
            return "unknown"

    async def preprocess_text_for_kg(self, text: str, filename: str = "") -> str:
        """为知识图谱构建预处理文本（支持长文本分段处理）"""
        if not settings.ENABLE_ENHANCED_PREPROCESSING:
            # 使用原有的简单预处理
            return await self._simple_preprocess_text(text, filename)

        # 检查文本长度，决定是否需要分段处理
        if len(text) <= settings.PREPROCESS_SEGMENT_SIZE:
            # 短文本，直接处理
            return await self._enhanced_preprocess_segment(text, filename, is_single=True)

        # 长文本，分段处理
        logger.info(f"文本长度 {len(text)} 超过限制，开始分段预处理")
        return await self._preprocess_long_text(text, filename)

    async def _simple_preprocess_text(self, text: str, filename: str = "") -> str:
        """简单预处理（原有逻辑）"""
        prompt = f"""
请对以下文本进行预处理，为知识图谱构建做准备。

要求：
1. 整理和结构化文本内容
2. 识别并标注重要的实体（人物、地点、组织、概念等）
3. 明确实体之间的关系
4. 保持原文的核心信息和语义
5. 输出结构化的文本，便于后续的实体关系抽取

文件名：{filename}

原始文本：
{text[:8000]}
"""

        messages = [
            {"role": "system", "content": "你是一个专业的文本预处理专家，擅长为知识图谱构建准备结构化文本。"},
            {"role": "user", "content": prompt}
        ]

        try:
            result = await self._call_llm(messages)
            return result.strip()
        except Exception as e:
            logger.error(f"文本预处理失败: {e}")
            return text  # 如果预处理失败，返回原文

    async def _preprocess_long_text(self, text: str, filename: str = "") -> str:
        """长文本分段预处理"""
        segments = self._split_text_into_segments(text)
        processed_segments = []

        logger.info(f"将文本分为 {len(segments)} 段进行预处理")

        # 限制处理的段数
        segments_to_process = segments[:settings.MAX_PREPROCESS_SEGMENTS]

        for i, segment in enumerate(segments_to_process):
            try:
                logger.info(f"预处理第 {i+1}/{len(segments_to_process)} 段")
                processed_segment = await self._enhanced_preprocess_segment(
                    segment,
                    filename,
                    segment_index=i+1,
                    total_segments=len(segments_to_process)
                )
                processed_segments.append(processed_segment)
            except Exception as e:
                logger.error(f"预处理第 {i+1} 段失败: {e}")
                # 如果预处理失败，使用原始段落
                processed_segments.append(f"=== 段落 {i+1} ===\n{segment}")

        # 合并所有处理后的段落
        return self._merge_processed_segments(processed_segments, filename)

    def _split_text_into_segments(self, text: str) -> List[str]:
        """将长文本分割为重叠的段落"""
        segments = []
        segment_size = settings.PREPROCESS_SEGMENT_SIZE
        overlap_size = settings.PREPROCESS_OVERLAP_SIZE

        start = 0
        while start < len(text):
            end = start + segment_size

            # 如果不是最后一段，尝试在句号或段落处分割
            if end < len(text):
                # 向后查找合适的分割点
                for i in range(min(200, len(text) - end)):
                    if text[end + i] in ['。', '\n\n', '！', '？']:
                        end = end + i + 1
                        break

            segment = text[start:end].strip()
            if segment:
                segments.append(segment)

            # 计算下一段的起始位置（考虑重叠）
            start = end - overlap_size
            if start >= len(text):
                break

        return segments

    async def _enhanced_preprocess_segment(
        self,
        segment: str,
        filename: str = "",
        is_single: bool = False,
        segment_index: int = 1,
        total_segments: int = 1
    ) -> str:
        """增强预处理单个段落"""
        segment_info = f"段落 {segment_index}/{total_segments}" if not is_single else "完整文档"

        prompt = f"""
请对以下文本段落进行深度预处理，为知识图谱构建做准备。

文档信息：
- 文件名：{filename}
- 处理范围：{segment_info}

预处理要求：
1. **实体识别与标注**：
   - 识别所有重要实体（人物、地点、组织、概念、事件、产品、时间等）
   - 为每个实体添加类型标注：[实体名称|类型]
   - 保留实体的完整描述信息

2. **关系识别与标注**：
   - 识别实体间的各种关系（工作关系、地理关系、从属关系、因果关系等）
   - 明确标注关系：[实体A] --关系类型--> [实体B]
   - 特别关注隐含的、跨句的关系

3. **结构化输出**：
   - 按主题组织内容
   - 保持逻辑层次清晰
   - 添加关系总结部分

4. **信息增强**：
   - 补充实体的背景信息
   - 明确时间、地点等上下文
   - 保留所有关键细节

输出格式要求：
```
## 主要实体
- [实体名|类型]: 描述信息

## 核心内容
（结构化的主要内容）

## 关系网络
- [实体A] --关系类型--> [实体B]: 关系描述
- ...

## 补充信息
（背景信息、上下文等）
```

原始文本：
{segment}
"""

        messages = [
            {"role": "system", "content": "你是一个专业的知识图谱预处理专家，擅长从文本中识别实体和关系，并进行结构化标注。"},
            {"role": "user", "content": prompt}
        ]

        try:
            result = await self._call_llm(messages)
            return result.strip()
        except Exception as e:
            logger.error(f"增强预处理失败: {e}")
            return f"=== {segment_info} ===\n{segment}"

    def _merge_processed_segments(self, processed_segments: List[str], filename: str = "") -> str:
        """合并处理后的段落"""
        merged_content = f"""# 知识图谱预处理结果
文件：{filename}
处理时间：{time.strftime('%Y-%m-%d %H:%M:%S')}
段落数量：{len(processed_segments)}

## 整体概述
本文档经过分段预处理，每个段落都进行了实体识别、关系标注和结构化整理。

"""

        for i, segment in enumerate(processed_segments):
            merged_content += f"\n\n{'='*50}\n"
            merged_content += f"段落 {i+1}\n"
            merged_content += f"{'='*50}\n"
            merged_content += segment

        # 添加全局关系总结
        merged_content += f"\n\n{'='*50}\n"
        merged_content += "全局关系总结\n"
        merged_content += f"{'='*50}\n"
        merged_content += "注意：以上各段落中的实体和关系可能存在跨段落的连接，在后续处理中需要进行实体消歧和关系合并。\n"

        return merged_content

    async def merge_multiple_texts_for_kg(self, texts: List[str], filenames: List[str] = None) -> str:
        """合并多个文本为知识图谱构建做准备（渐进式整合）"""
        if filenames is None:
            filenames = [f"文件{i+1}" for i in range(len(texts))]

        if len(texts) == 0:
            return ""

        if len(texts) == 1:
            return f"=== {filenames[0]} ===\n{texts[0]}"

        logger.info(f"开始渐进式整合 {len(texts)} 个文件")

        # 渐进式整合：先处理前两个文件，然后逐个添加后续文件
        try:
            # 第一步：整合前两个文件
            merged_content = await self._merge_two_texts(
                texts[0], texts[1], filenames[0], filenames[1]
            )
            logger.info(f"完成前两个文件的整合: {filenames[0]} + {filenames[1]}")

            # 第二步：逐个添加剩余文件
            for i in range(2, len(texts)):
                merged_content = await self._merge_existing_with_new_text(
                    merged_content, texts[i], f"已整合内容", filenames[i]
                )
                logger.info(f"添加文件 {filenames[i]} 到整合内容中")

            return merged_content

        except Exception as e:
            logger.error(f"渐进式整合失败: {e}")
            # 如果整合失败，回退到简单拼接
            return "\n\n".join([f"=== {filename} ===\n{text}" for text, filename in zip(texts, filenames)])

    async def _merge_two_texts(self, text1: str, text2: str, filename1: str, filename2: str) -> str:
        """合并两个文本"""
        # 限制文本长度以避免超出LLM处理能力
        max_length = 2500
        truncated_text1 = text1[:max_length]
        truncated_text2 = text2[:max_length]

        prompt = f"""
请将以下两个文件的内容整合为一个统一的、结构化的文本，为知识图谱构建做准备。

要求：
1. 识别两个文件中的共同实体和概念
2. 建立文件间的关联和引用关系
3. 消除重复信息，保留关键内容
4. 输出结构化的整合文本，包含实体标注和关系描述
5. 保持原始信息的完整性和准确性

文件1 ({filename1}):
{truncated_text1}

文件2 ({filename2}):
{truncated_text2}

请输出整合后的结构化文本：
"""

        messages = [
            {"role": "system", "content": "你是一个专业的文本整合专家，擅长识别文档间的关联并进行知识整合。"},
            {"role": "user", "content": prompt}
        ]

        try:
            result = await self._call_llm(messages)
            return result.strip()
        except Exception as e:
            logger.error(f"两文本合并失败: {e}")
            return f"=== {filename1} ===\n{text1}\n\n=== {filename2} ===\n{text2}"

    async def _merge_existing_with_new_text(self, existing_content: str, new_text: str,
                                          existing_label: str, new_filename: str) -> str:
        """将新文本整合到已有内容中"""
        # 限制文本长度
        max_existing_length = 2000
        max_new_length = 2500

        truncated_existing = existing_content[:max_existing_length]
        truncated_new = new_text[:max_new_length]

        prompt = f"""
请将新文件的内容整合到已有的整合内容中，形成更完整的知识结构。

要求：
1. 识别新文件与已有内容的关联点
2. 将新文件中的实体和关系融入现有结构
3. 避免重复信息，增强关系网络
4. 保持整合内容的结构化和逻辑性
5. 突出跨文件的实体关系

已有整合内容:
{truncated_existing}

新文件 ({new_filename}):
{truncated_new}

请输出更新后的整合内容：
"""

        messages = [
            {"role": "system", "content": "你是一个专业的知识整合专家，擅长将新信息融入现有知识结构中。"},
            {"role": "user", "content": prompt}
        ]

        try:
            result = await self._call_llm(messages)
            return result.strip()
        except Exception as e:
            logger.error(f"新文本整合失败: {e}")
            return f"{existing_content}\n\n=== {new_filename} ===\n{new_text}"

    def _clean_json_response(self, json_str: str) -> str:
        """清理和修复JSON响应"""
        # 移除可能的markdown代码块标记
        json_str = re.sub(r'```json\s*', '', json_str)
        json_str = re.sub(r'```\s*$', '', json_str)

        # 移除多余的空白字符
        json_str = json_str.strip()

        # 尝试修复常见的JSON格式问题
        # 修复单引号为双引号
        json_str = re.sub(r"'([^']*)':", r'"\1":', json_str)
        json_str = re.sub(r":\s*'([^']*)'", r': "\1"', json_str)

        # 修复缺失的逗号
        json_str = re.sub(r'}\s*{', '}, {', json_str)
        json_str = re.sub(r']\s*{', '], {', json_str)
        json_str = re.sub(r'}\s*\[', '}, [', json_str)

        return json_str
