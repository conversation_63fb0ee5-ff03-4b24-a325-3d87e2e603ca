"""
知识图谱API路由
"""
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Query, UploadFile, File
from datetime import datetime

from app.services.knowledge_graph_service import KnowledgeGraphService
from app.services.file_service import FileService
from app.models.knowledge_graph import (
    KnowledgeGraph, GraphSearchResult, GraphStats
)
from app.models.file_models import UploadResponse

router = APIRouter()
kg_service = KnowledgeGraphService()

@router.get("/list")
async def list_knowledge_graphs():
    """获取所有知识图谱列表"""
    try:
        graphs = kg_service.list_knowledge_graphs()
        return {"graphs": graphs}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取图谱列表失败: {str(e)}")

@router.get("/{kg_id}", response_model=KnowledgeGraph)
async def get_knowledge_graph(kg_id: str):
    """获取指定知识图谱"""
    try:
        kg = await kg_service.load_knowledge_graph(kg_id)
        if not kg:
            raise HTTPException(status_code=404, detail="知识图谱不存在")
        return kg
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取知识图谱失败: {str(e)}")

@router.get("/{kg_id}/stats", response_model=GraphStats)
async def get_graph_stats(kg_id: str):
    """获取图谱统计信息"""
    try:
        # 确保图谱已加载
        kg = await kg_service.load_knowledge_graph(kg_id)
        if not kg:
            raise HTTPException(status_code=404, detail="知识图谱不存在")
        
        stats = kg_service.get_graph_stats(kg_id)
        if not stats:
            raise HTTPException(status_code=404, detail="无法获取图谱统计信息")
        
        return stats
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@router.get("/{kg_id}/search", response_model=GraphSearchResult)
async def search_entities(
    kg_id: str, 
    query: str = Query(..., description="搜索关键词"),
    limit: int = Query(10, description="结果数量限制", ge=1, le=100)
):
    """搜索实体"""
    try:
        # 确保图谱已加载
        kg = await kg_service.load_knowledge_graph(kg_id)
        if not kg:
            raise HTTPException(status_code=404, detail="知识图谱不存在")
        
        result = kg_service.search_entities(kg_id, query, limit)
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@router.get("/{kg_id}/entity/{entity_id}/neighbors", response_model=GraphSearchResult)
async def get_entity_neighbors(
    kg_id: str, 
    entity_id: str,
    depth: int = Query(1, description="搜索深度", ge=1, le=3)
):
    """获取实体的邻居节点"""
    try:
        # 确保图谱已加载
        kg = await kg_service.load_knowledge_graph(kg_id)
        if not kg:
            raise HTTPException(status_code=404, detail="知识图谱不存在")
        
        result = kg_service.get_entity_neighbors(kg_id, entity_id, depth)
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取邻居节点失败: {str(e)}")

@router.get("/{kg_id}/export")
async def export_knowledge_graph(kg_id: str, format: str = Query("json", description="导出格式")):
    """导出知识图谱"""
    try:
        kg = await kg_service.load_knowledge_graph(kg_id)
        if not kg:
            raise HTTPException(status_code=404, detail="知识图谱不存在")
        
        if format.lower() == "json":
            return kg.dict()
        elif format.lower() == "networkx":
            # 转换为NetworkX格式
            G = kg.to_networkx()
            return kg_service._networkx_to_dict(G)
        else:
            raise HTTPException(status_code=400, detail="不支持的导出格式")
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

@router.delete("/{kg_id}")
async def delete_knowledge_graph(kg_id: str):
    """删除知识图谱"""
    try:
        from pathlib import Path
        from app.core.config import settings
        
        # 删除文件
        file_path = Path(settings.KG_DATA_DIR) / f"{kg_id}.json"
        if file_path.exists():
            file_path.unlink()
        
        # 从内存中移除
        if kg_id in kg_service.graphs:
            del kg_service.graphs[kg_id]
        
        return {"message": "知识图谱删除成功"}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")

@router.post("/{kg_id}/merge/{other_kg_id}")
async def merge_knowledge_graphs(kg_id: str, other_kg_id: str):
    """合并两个知识图谱"""
    try:
        # 加载两个图谱
        kg1 = await kg_service.load_knowledge_graph(kg_id)
        kg2 = await kg_service.load_knowledge_graph(other_kg_id)
        
        if not kg1 or not kg2:
            raise HTTPException(status_code=404, detail="知识图谱不存在")
        
        # 合并实体和关系
        merged_entities = kg1.entities + kg2.entities
        merged_relations = kg1.relations + kg2.relations
        
        # 去重处理
        merged_entities = kg_service._merge_duplicate_entities(merged_entities)
        
        # 创建新的合并图谱
        import uuid
        merged_kg = KnowledgeGraph(
            id=str(uuid.uuid4()),
            name=f"{kg1.name}_merged_{kg2.name}",
            entities=merged_entities,
            relations=merged_relations,
            metadata={
                "merged_from": [kg_id, other_kg_id],
                "merge_time": str(datetime.now())
            }
        )
        
        # 保存合并后的图谱
        await kg_service.save_knowledge_graph(merged_kg)
        kg_service.graphs[merged_kg.id] = merged_kg
        
        from datetime import datetime

        return {
            "message": "图谱合并成功",
            "merged_kg_id": merged_kg.id,
            "entity_count": len(merged_entities),
            "relation_count": len(merged_relations)
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"合并失败: {str(e)}")


@router.post("/{kg_id}/update")
async def update_knowledge_graph_with_file(
    kg_id: str,
    file: UploadFile = File(...)
):
    """基于现有知识图谱和新文件创建更新后的新图谱"""
    from loguru import logger
    import uuid
    from datetime import datetime

    try:
        logger.info(f"开始基于知识图谱 {kg_id} 创建更新图谱，文件: {file.filename}")

        # 检查原图谱是否存在
        existing_kg = await kg_service.load_knowledge_graph(kg_id)
        if not existing_kg:
            logger.error(f"原知识图谱不存在: {kg_id}")
            raise HTTPException(status_code=404, detail="原知识图谱不存在")

        # 初始化文件服务
        file_service = FileService()

        # 验证文件
        if not file.filename:
            logger.error("文件名为空")
            raise HTTPException(status_code=400, detail="文件名不能为空")

        logger.info(f"文件信息: 名称={file.filename}, 类型={file.content_type}")

        # 读取并保存上传的文件
        file_content = await file.read()
        logger.info(f"文件大小: {len(file_content)} bytes")

        if len(file_content) == 0:
            logger.error("文件内容为空")
            raise HTTPException(status_code=400, detail="文件内容为空")

        file_info = await file_service.save_uploaded_file(file_content, file.filename)
        logger.info(f"文件保存成功: {file_info.file_path}")

        # 提取文本内容
        logger.info("开始提取文本内容...")
        text = await file_service.extract_text_from_file(file_info)
        logger.info(f"文本提取成功，长度: {len(text)} 字符")

        if len(text.strip()) == 0:
            logger.error("提取的文本内容为空")
            raise HTTPException(status_code=400, detail="文件中没有可提取的文本内容")

        # 从新文件构建临时知识图谱
        logger.info("开始从新文件构建知识图谱...")
        temp_kg_name = f"temp_new_content_{kg_id}"
        try:
            new_content_kg = await kg_service.build_knowledge_graph_with_preprocessing(
                text,
                file.filename,
                temp_kg_name,
                task_id=f"update_{kg_id}"
            )
            logger.info(f"新内容图谱构建成功: {len(new_content_kg.entities)} 个实体, {len(new_content_kg.relations)} 个关系")
        except Exception as e:
            logger.error(f"构建新内容知识图谱失败: {e}")
            # 清理上传的文件
            try:
                await file_service.delete_file(file_info.file_path)
            except:
                pass
            raise HTTPException(status_code=500, detail=f"知识图谱构建失败: {str(e)}")

        # 提取原图谱的实体和关系
        logger.info("提取原图谱数据...")
        original_entities = existing_kg.entities.copy()
        original_relations = existing_kg.relations.copy()
        logger.info(f"原图谱数据: {len(original_entities)} 个实体, {len(original_relations)} 个关系")

        # 合并原图谱和新图谱的数据
        logger.info("开始合并图谱数据...")
        merged_entities = original_entities + new_content_kg.entities
        merged_relations = original_relations + new_content_kg.relations
        logger.info(f"合并前: 原图谱 {len(original_entities)} 实体, {len(original_relations)} 关系")
        logger.info(f"合并前: 新内容 {len(new_content_kg.entities)} 实体, {len(new_content_kg.relations)} 关系")

        # 使用增强的实体合并和关系发现逻辑
        logger.info("开始智能实体合并和关系增强处理...")
        try:
            # 第一步：智能合并重复实体
            logger.info("第一步：智能合并重复实体...")
            merged_entities = kg_service._merge_duplicate_entities(merged_entities)
            logger.info(f"实体合并完成，合并后实体数: {len(merged_entities)}")

            # 第二步：更新关系中的实体ID
            logger.info("第二步：更新关系中的实体ID...")
            updated_relations = kg_service._update_relation_entity_ids(merged_relations, merged_entities)
            logger.info(f"关系ID更新完成，有效关系数: {len(updated_relations)}")

            # 第三步：增强关系发现
            logger.info("第三步：增强关系发现...")
            enhanced_relations = await kg_service._enhance_relations_after_merge(merged_entities, updated_relations)
            logger.info(f"关系增强完成，最终关系数: {len(enhanced_relations)}")

            merged_relations = enhanced_relations

        except Exception as e:
            logger.error(f"智能合并处理失败: {e}")
            logger.exception("详细错误信息:")
            # 回退到增强的实体消歧处理
            try:
                logger.info("回退到增强的实体消歧处理...")
                final_entities, id_mapping, unmatched_relations = await kg_service._enhanced_entity_disambiguation(merged_entities, merged_relations)
                final_relations = kg_service._update_relation_entity_ids_with_mapping(merged_relations, final_entities, id_mapping)

                if unmatched_relations:
                    additional_relations = kg_service.disambiguation_service.fuzzy_match_entities(unmatched_relations, final_entities)
                    final_relations.extend(additional_relations)

                merged_entities = final_entities
                merged_relations = final_relations
                logger.info(f"回退处理完成: {len(merged_entities)} 个实体, {len(merged_relations)} 个关系")

            except Exception as e2:
                logger.error(f"回退处理也失败: {e2}")
                # 最后回退到简单处理
                merged_entities = kg_service._merge_duplicate_entities(merged_entities)
                merged_relations = kg_service._update_relation_entity_ids(merged_relations, merged_entities)
                logger.warning(f"使用简单处理: {len(merged_entities)} 个实体, {len(merged_relations)} 个关系")

        # 生成新图谱ID和名称
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        new_kg_id = str(uuid.uuid4())
        new_kg_name = f"update_{timestamp}"

        logger.info(f"创建新图谱: ID={new_kg_id}, 名称={new_kg_name}")

        # 创建新的知识图谱
        from app.models.knowledge_graph import KnowledgeGraph
        new_kg = KnowledgeGraph(
            id=new_kg_id,
            name=new_kg_name,
            entities=merged_entities,
            relations=merged_relations,
            metadata={
                "created_from": "update_operation",
                "original_kg_id": kg_id,
                "original_kg_name": existing_kg.name,
                "update_file": file.filename,
                "update_time": str(datetime.now()),
                "original_entities_count": len(original_entities),
                "original_relations_count": len(original_relations),
                "new_entities_count": len(new_content_kg.entities),
                "new_relations_count": len(new_content_kg.relations),
                "final_entities_count": len(merged_entities),
                "final_relations_count": len(merged_relations)
            }
        )

        # 保存新图谱
        await kg_service.save_knowledge_graph(new_kg)
        kg_service.graphs[new_kg_id] = new_kg
        logger.info(f"新图谱保存成功: {new_kg_id}")

        # 清理上传的文件
        try:
            await file_service.delete_file(file_info.file_path)
        except Exception as e:
            logger.warning(f"清理文件时出错: {e}")

        return {
            "message": "基于原图谱创建更新图谱成功",
            "original_kg_id": kg_id,
            "new_kg_id": new_kg_id,
            "new_kg_name": new_kg_name,
            "total_entities": len(merged_entities),
            "total_relations": len(merged_relations),
            "original_entities": len(original_entities),
            "original_relations": len(original_relations),
            "entities_added": len(new_content_kg.entities),
            "relations_added": len(new_content_kg.relations)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建更新图谱失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建更新图谱失败: {str(e)}")
