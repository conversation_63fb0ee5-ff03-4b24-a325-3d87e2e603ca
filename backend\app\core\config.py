"""
系统配置
"""
import os
from typing import Optional
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    """应用配置"""
    
    # 应用基本配置
    APP_NAME: str = "知识图谱构建系统"
    VERSION: str = "1.0.0"
    DEBUG: bool = True
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # LLM API配置
    LLM_API_BASE: str = "https://gateway.chat.sensedeal.vip/v1"
    LLM_API_KEY: str = "974fd8d1c155aa3d04b17bf253176b5e"
    LLM_MODEL: str = "qwen2.5-32b-instruct"
    LLM_MAX_TOKENS: int = 4000
    LLM_TEMPERATURE: float = 0.1
    
    # 文件处理配置
    UPLOAD_DIR: str = "data/uploads"
    MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
    ALLOWED_EXTENSIONS: list = [
        ".txt", ".pdf", ".docx", ".doc", 
        ".xlsx", ".xls", ".csv", ".json"
    ]
    
    # 知识图谱配置
    KG_DATA_DIR: str = "data/knowledge_graph"
    MAX_ENTITIES_PER_CHUNK: int = 50
    MAX_RELATIONS_PER_CHUNK: int = 100
    CHUNK_SIZE: int = 3000  # 增加块大小，减少块数量
    CHUNK_OVERLAP: int = 300
    MAX_CHUNKS_TO_PROCESS: int = 50  # 限制处理的块数量

    # 并发处理配置
    MAX_CONCURRENT_CHUNKS: int = 8  # 最大并发处理的文本块数量
    BATCH_SIZE: int = 4  # 批处理大小
    LLM_REQUEST_TIMEOUT: int = 120  # LLM请求超时时间（秒）
    LLM_MAX_RETRIES: int = 3  # LLM请求最大重试次数
    LLM_RETRY_DELAY: float = 1.0  # 重试延迟（秒）
    CONNECTION_POOL_SIZE: int = 20  # HTTP连接池大小
    ENABLE_PARALLEL_PROCESSING: bool = True  # 是否启用并行处理

    # 文本预处理配置
    PREPROCESS_SEGMENT_SIZE: int = 6000  # 预处理分段大小
    PREPROCESS_OVERLAP_SIZE: int = 800  # 预处理分段重叠大小
    MAX_PREPROCESS_SEGMENTS: int = 10  # 最大预处理分段数量
    ENABLE_ENHANCED_PREPROCESSING: bool = True  # 启用增强预处理
    RELATION_EXTRACTION_FOCUS: bool = True  # 关注关系提取优化

    # 实体消歧和关系发现配置
    ENABLE_ENTITY_DISAMBIGUATION: bool = True  # 启用实体消歧
    ENABLE_LLM_ENTITY_DISAMBIGUATION: bool = True  # 启用LLM实体变体识别
    ENTITY_SIMILARITY_THRESHOLD: float = 0.8  # 实体相似度阈值
    LLM_ENTITY_CONFIDENCE_THRESHOLD: float = 0.7  # LLM实体变体识别置信度阈值
    ENABLE_RELATION_INFERENCE: bool = True  # 启用关系推理
    ENABLE_CONNECTIVITY_CHECK: bool = True  # 启用连通性检查
    MAX_ENTITY_VARIANTS: int = 5  # 每个实体最大变体数量
    FUZZY_MATCH_THRESHOLD: float = 0.7  # 模糊匹配阈值

    # 实体标准化配置
    ENABLE_ENTITY_STANDARDIZATION: bool = True  # 启用实体标准化
    ENTITY_COMPLETION_THRESHOLD: float = 0.85  # 实体完整性阈值
    ENABLE_ENTITY_COMPLETION: bool = True  # 启用实体补全
    ENTITY_TEMP_FILE_FORMAT: str = "structured"  # 临时文件格式：structured/simple
    MIN_ENTITY_NAME_LENGTH: int = 2  # 最小实体名称长度
    MAX_ENTITY_NAME_LENGTH: int = 50  # 最大实体名称长度
    ENABLE_ENTITY_VALIDATION: bool = True  # 启用实体验证
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# 创建全局配置实例
settings = Settings()

# 确保必要的目录存在
os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
os.makedirs(settings.KG_DATA_DIR, exist_ok=True)
os.makedirs("logs", exist_ok=True)
