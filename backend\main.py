"""
知识图谱构建系统 - 主应用入口
"""
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from app.api import files, knowledge_graph, llm, progress
from app.core.config import settings

# 创建FastAPI应用
app = FastAPI(
    title="知识图谱构建系统",
    description="基于大语言模型的智能知识图谱构建和可视化系统",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务
app.mount("/uploads", StaticFiles(directory="data/uploads"), name="uploads")

# 注册路由
app.include_router(files.router, prefix="/api/files", tags=["文件处理"])
app.include_router(knowledge_graph.router, prefix="/api/kg", tags=["知识图谱"])
app.include_router(llm.router, prefix="/api/llm", tags=["大语言模型"])
app.include_router(progress.router, prefix="/api/progress", tags=["进度跟踪"])

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "知识图谱构建系统API",
        "version": "1.0.0",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy"}

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
