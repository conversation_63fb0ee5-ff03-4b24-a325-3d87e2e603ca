"""
大语言模型API路由
"""
from typing import List, Dict, Any
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from app.services.llm_service import LLMService

router = APIRouter()
llm_service = LLMService()

class TextAnalysisRequest(BaseModel):
    """文本分析请求"""
    text: str
    analysis_type: str = "entities_relations"  # entities_relations, summary, classification

class TextAnalysisResponse(BaseModel):
    """文本分析响应"""
    success: bool
    analysis_type: str
    result: Dict[str, Any]
    message: str

class EntityClassificationRequest(BaseModel):
    """实体分类请求"""
    entity_name: str
    context: str = ""

@router.post("/analyze", response_model=TextAnalysisResponse)
async def analyze_text(request: TextAnalysisRequest):
    """分析文本"""
    try:
        if request.analysis_type == "entities_relations":
            entities, relations = await llm_service.extract_entities_and_relations(request.text)
            result = {
                "entities": [entity.dict() for entity in entities],
                "relations": [relation.dict() for relation in relations]
            }
        elif request.analysis_type == "summary":
            summary = await llm_service.summarize_text(request.text)
            result = {"summary": summary}
        else:
            raise HTTPException(status_code=400, detail="不支持的分析类型")
        
        return TextAnalysisResponse(
            success=True,
            analysis_type=request.analysis_type,
            result=result,
            message="分析完成"
        )
    
    except Exception as e:
        return TextAnalysisResponse(
            success=False,
            analysis_type=request.analysis_type,
            result={},
            message=f"分析失败: {str(e)}"
        )

@router.post("/classify-entity")
async def classify_entity(request: EntityClassificationRequest):
    """实体类型分类"""
    try:
        entity_type = await llm_service.classify_entity_type(
            request.entity_name, 
            request.context
        )
        
        return {
            "success": True,
            "entity_name": request.entity_name,
            "entity_type": entity_type,
            "message": "分类完成"
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"实体分类失败: {str(e)}")

@router.get("/models")
async def get_available_models():
    """获取可用的模型列表"""
    try:
        import httpx
        from app.core.config import settings
        
        headers = {
            "Authorization": f"Bearer {settings.LLM_API_KEY}",
            "Content-Type": "application/json"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{settings.LLM_API_BASE}/models",
                headers=headers
            )
            response.raise_for_status()
            return response.json()
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模型列表失败: {str(e)}")

@router.get("/config")
async def get_llm_config():
    """获取LLM配置信息"""
    from app.core.config import settings
    
    return {
        "api_base": settings.LLM_API_BASE,
        "model": settings.LLM_MODEL,
        "max_tokens": settings.LLM_MAX_TOKENS,
        "temperature": settings.LLM_TEMPERATURE
    }

@router.post("/test-connection")
async def test_llm_connection():
    """测试LLM连接"""
    try:
        # 发送一个简单的测试请求
        test_messages = [
            {"role": "user", "content": "Hello, please respond with 'Connection successful'"}
        ]
        
        response = await llm_service._call_llm(test_messages)
        
        return {
            "success": True,
            "message": "连接成功",
            "response": response
        }
    
    except Exception as e:
        return {
            "success": False,
            "message": f"连接失败: {str(e)}"
        }
