"""
文件处理相关数据模型
"""
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

class FileType(str, Enum):
    """文件类型枚举"""
    TXT = "txt"
    PDF = "pdf"
    DOCX = "docx"
    DOC = "doc"
    XLSX = "xlsx"
    XLS = "xls"
    CSV = "csv"
    JSON = "json"
    WECHAT = "wechat"

class ProcessStatus(str, Enum):
    """处理状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class FileInfo(BaseModel):
    """文件信息模型"""
    id: str = Field(..., description="文件唯一标识")
    filename: str = Field(..., description="文件名")
    original_name: str = Field(..., description="原始文件名")
    file_type: FileType = Field(..., description="文件类型")
    file_size: int = Field(..., description="文件大小(字节)")
    file_path: str = Field(..., description="文件路径")
    upload_time: datetime = Field(default_factory=datetime.now, description="上传时间")
    status: ProcessStatus = Field(default=ProcessStatus.PENDING, description="处理状态")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="文件元数据")

class TextChunk(BaseModel):
    """文本块模型"""
    id: str = Field(..., description="文本块ID")
    content: str = Field(..., description="文本内容")
    start_pos: int = Field(..., description="起始位置")
    end_pos: int = Field(..., description="结束位置")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")

class ProcessResult(BaseModel):
    """处理结果模型"""
    file_id: str = Field(..., description="文件ID")
    status: ProcessStatus = Field(..., description="处理状态")
    text_chunks: List[TextChunk] = Field(default_factory=list, description="文本块列表")
    extracted_entities: List[Dict[str, Any]] = Field(default_factory=list, description="提取的实体")
    extracted_relations: List[Dict[str, Any]] = Field(default_factory=list, description="提取的关系")
    error_message: Optional[str] = Field(None, description="错误信息")
    processing_time: Optional[float] = Field(None, description="处理时间(秒)")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")

class UploadResponse(BaseModel):
    """上传响应模型"""
    success: bool = Field(..., description="是否成功")
    file_info: Optional[FileInfo] = Field(None, description="文件信息")
    message: str = Field(..., description="响应消息")

class BatchProcessRequest(BaseModel):
    """批量处理请求模型"""
    file_ids: List[str] = Field(..., description="文件ID列表")
    options: Dict[str, Any] = Field(default_factory=dict, description="处理选项")

class FolderScanResult(BaseModel):
    """文件夹扫描结果"""
    total_files: int = Field(..., description="总文件数")
    supported_files: List[str] = Field(default_factory=list, description="支持的文件列表")
    unsupported_files: List[str] = Field(default_factory=list, description="不支持的文件列表")
    total_size: int = Field(..., description="总大小(字节)")

class WeChatMessage(BaseModel):
    """微信消息模型"""
    timestamp: datetime = Field(..., description="时间戳")
    sender: str = Field(..., description="发送者")
    content: str = Field(..., description="消息内容")
    message_type: str = Field(default="text", description="消息类型")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
