"""
实体消歧和关系发现服务
"""
import re
import difflib
from typing import List, Dict, Set, Tuple, Optional
from collections import defaultdict
import networkx as nx
from loguru import logger

from app.core.config import settings
from app.models.knowledge_graph import Entity, Relation, KnowledgeGraph
from app.services.llm_service import LLMService


class EntityDisambiguationService:
    """实体消歧和关系发现服务"""
    
    def __init__(self, llm_service: LLMService):
        self.llm_service = llm_service
        
    def calculate_entity_similarity(self, entity1: Entity, entity2: Entity) -> float:
        """计算两个实体的相似度（基础版本，用于快速筛选）"""
        # 名称相似度（权重40%）
        name_similarity = difflib.SequenceMatcher(None,
            entity1.name.lower().strip(),
            entity2.name.lower().strip()
        ).ratio()

        # 类型相似度（权重20%）
        type_similarity = 1.0 if entity1.type == entity2.type else 0.0

        # 描述相似度（权重40%）
        desc_similarity = 0.0
        if entity1.description and entity2.description:
            desc_similarity = difflib.SequenceMatcher(None,
                entity1.description.lower(),
                entity2.description.lower()
            ).ratio()

        # 综合相似度
        total_similarity = (
            name_similarity * 0.4 +
            type_similarity * 0.2 +
            desc_similarity * 0.4
        )

        return total_similarity

    async def llm_based_entity_similarity(self, entity1: Entity, entity2: Entity) -> float:
        """使用LLM判断两个实体是否为同一实体的变体"""
        prompt = f"""
请判断以下两个实体是否指向同一个真实世界的对象（即是否为同一实体的不同表述）。

实体1:
- 名称: {entity1.name}
- 类型: {entity1.type or "未知"}
- 描述: {entity1.description or "无描述"}

实体2:
- 名称: {entity2.name}
- 类型: {entity2.type or "未知"}
- 描述: {entity2.description or "无描述"}

判断标准:
1. 是否指向同一个人、组织、地点、概念等
2. 是否为同一实体的不同名称、简称、全称等
3. 是否为同一实体在不同语言或表述方式下的形式

请返回JSON格式的结果:
{{
    "is_same_entity": true/false,
    "confidence": 0.0-1.0,
    "reason": "判断理由"
}}

只返回JSON，不要其他内容。
"""

        try:
            messages = [
                {"role": "system", "content": "你是一个专业的实体识别专家，擅长判断不同表述是否指向同一实体。"},
                {"role": "user", "content": prompt}
            ]

            response = await self.llm_service._call_llm(messages)

            # 解析JSON响应
            import json
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                data = json.loads(json_match.group())
                is_same = data.get("is_same_entity", False)
                confidence = data.get("confidence", 0.0)

                # 如果LLM认为是同一实体，返回高相似度；否则返回低相似度
                return confidence if is_same else 0.0
            else:
                logger.warning(f"LLM响应格式错误: {response}")
                return 0.0

        except Exception as e:
            logger.error(f"LLM实体相似度判断失败: {e}")
            # 回退到基础相似度计算
            return self.calculate_entity_similarity(entity1, entity2)
    
    def find_entity_variants(self, entities: List[Entity]) -> Dict[str, List[Entity]]:
        """找到实体的变体（同一实体的不同表述）- 基础版本"""
        entity_groups = {}
        processed = set()

        for i, entity in enumerate(entities):
            if entity.id in processed:
                continue

            # 创建新的实体组
            group_id = entity.id
            entity_groups[group_id] = [entity]
            processed.add(entity.id)

            # 查找相似实体
            for j, other_entity in enumerate(entities[i+1:], i+1):
                if other_entity.id in processed:
                    continue

                similarity = self.calculate_entity_similarity(entity, other_entity)

                if similarity >= settings.ENTITY_SIMILARITY_THRESHOLD:
                    entity_groups[group_id].append(other_entity)
                    processed.add(other_entity.id)
                    logger.debug(f"发现实体变体: {entity.name} <-> {other_entity.name} (相似度: {similarity:.3f})")

        # 过滤只有一个实体的组
        return {k: v for k, v in entity_groups.items() if len(v) > 1}

    async def find_entity_variants_with_llm(self, entities: List[Entity]) -> Dict[str, List[Entity]]:
        """使用LLM找到实体的变体（更准确的版本）"""
        from loguru import logger

        logger.info(f"开始使用LLM进行实体变体识别，共 {len(entities)} 个实体")

        entity_groups = {}
        processed = set()

        # 首先使用基础相似度进行快速筛选，减少LLM调用次数
        candidates = []
        for i, entity in enumerate(entities):
            for j, other_entity in enumerate(entities[i+1:], i+1):
                basic_similarity = self.calculate_entity_similarity(entity, other_entity)
                # 降低初筛阈值，让更多候选进入LLM判断
                if basic_similarity >= 0.3:  # 降低阈值
                    candidates.append((entity, other_entity, basic_similarity))

        logger.info(f"基础筛选后有 {len(candidates)} 对候选实体需要LLM判断")

        # 使用LLM对候选实体对进行精确判断
        confirmed_pairs = []
        for i, (entity1, entity2, basic_sim) in enumerate(candidates):
            try:
                llm_similarity = await self.llm_based_entity_similarity(entity1, entity2)

                # LLM判断为同一实体的阈值
                if llm_similarity >= settings.LLM_ENTITY_CONFIDENCE_THRESHOLD:
                    confirmed_pairs.append((entity1, entity2, llm_similarity))
                    logger.info(f"LLM确认实体变体: {entity1.name} <-> {entity2.name} (置信度: {llm_similarity:.3f})")
                else:
                    logger.debug(f"LLM否定实体变体: {entity1.name} <-> {entity2.name} (置信度: {llm_similarity:.3f})")

                # 每10个判断后稍作休息，避免API限制
                if (i + 1) % 10 == 0:
                    import asyncio
                    await asyncio.sleep(0.1)

            except Exception as e:
                logger.error(f"LLM判断实体变体失败: {entity1.name} <-> {entity2.name}, 错误: {e}")
                continue

        # 构建实体组
        for i, entity in enumerate(entities):
            if entity.id in processed:
                continue

            # 创建新的实体组
            group_id = entity.id
            entity_groups[group_id] = [entity]
            processed.add(entity.id)

            # 查找确认的变体
            for entity1, entity2, similarity in confirmed_pairs:
                if entity1.id == entity.id and entity2.id not in processed:
                    entity_groups[group_id].append(entity2)
                    processed.add(entity2.id)
                elif entity2.id == entity.id and entity1.id not in processed:
                    entity_groups[group_id].append(entity1)
                    processed.add(entity1.id)

        # 过滤只有一个实体的组
        result = {k: v for k, v in entity_groups.items() if len(v) > 1}
        logger.info(f"LLM实体变体识别完成，发现 {len(result)} 个变体组")

        return result
    
    def merge_entity_variants(self, entity_variants: Dict[str, List[Entity]]) -> Dict[str, Entity]:
        """合并实体变体，返回原实体ID到合并后实体的映射"""
        merged_entities = {}
        id_mapping = {}
        
        for group_id, variants in entity_variants.items():
            # 选择最完整的实体作为主实体
            primary_entity = max(variants, key=lambda e: len(e.description or ""))
            
            # 合并所有变体的信息
            merged_name = primary_entity.name
            merged_description = primary_entity.description or ""
            merged_properties = primary_entity.properties.copy()
            
            # 收集所有变体的名称
            variant_names = [e.name for e in variants if e.name != primary_entity.name]
            if variant_names:
                merged_properties["variants"] = variant_names
            
            # 合并描述
            for variant in variants:
                if variant.description and variant.description not in merged_description:
                    merged_description += f"; {variant.description}"
                
                # 合并属性
                merged_properties.update(variant.properties)
            
            # 创建合并后的实体
            merged_entity = Entity(
                id=primary_entity.id,
                name=merged_name,
                type=primary_entity.type,
                description=merged_description,
                properties=merged_properties
            )
            
            merged_entities[primary_entity.id] = merged_entity
            
            # 建立ID映射
            for variant in variants:
                id_mapping[variant.id] = primary_entity.id
        
        return merged_entities, id_mapping
    
    def fuzzy_match_entities(self, unmatched_relations: List[Dict], entities: List[Entity]) -> List[Relation]:
        """使用模糊匹配为未匹配的关系找到实体"""
        matched_relations = []
        entity_name_map = {entity.name.lower().strip(): entity.id for entity in entities}
        
        # 创建实体名称的变体映射
        entity_variants_map = {}
        for entity in entities:
            variants = [entity.name.lower().strip()]
            
            # 添加常见变体
            name = entity.name.lower().strip()
            variants.extend([
                name.replace(" ", ""),  # 去空格
                name.replace("-", " "),  # 连字符转空格
                name.replace("_", " "),  # 下划线转空格
            ])
            
            # 如果有变体属性，也加入
            if "variants" in entity.properties:
                variants.extend([v.lower().strip() for v in entity.properties["variants"]])
            
            for variant in variants:
                entity_variants_map[variant] = entity.id
        
        for rel_data in unmatched_relations:
            source_name = rel_data["source"].lower().strip()
            target_name = rel_data["target"].lower().strip()
            
            source_id = None
            target_id = None
            
            # 精确匹配
            if source_name in entity_variants_map:
                source_id = entity_variants_map[source_name]
            if target_name in entity_variants_map:
                target_id = entity_variants_map[target_name]
            
            # 模糊匹配
            if not source_id:
                source_id = self._fuzzy_match_entity_name(source_name, entity_variants_map)
            if not target_id:
                target_id = self._fuzzy_match_entity_name(target_name, entity_variants_map)
            
            if source_id and target_id:
                import uuid
                relation = Relation(
                    id=str(uuid.uuid4()),
                    source_entity=source_id,
                    target_entity=target_id,
                    relation_type=rel_data.get("relation", "related"),
                    properties={
                        "description": rel_data.get("description", ""),
                        "matched_by": "fuzzy_matching"
                    }
                )
                matched_relations.append(relation)
                logger.debug(f"模糊匹配成功: {rel_data['source']} -> {rel_data['target']}")
        
        return matched_relations
    
    def _fuzzy_match_entity_name(self, name: str, entity_map: Dict[str, str]) -> Optional[str]:
        """模糊匹配实体名称"""
        best_match = None
        best_ratio = 0.0
        
        for entity_name in entity_map.keys():
            ratio = difflib.SequenceMatcher(None, name, entity_name).ratio()
            if ratio > best_ratio and ratio >= settings.FUZZY_MATCH_THRESHOLD:
                best_ratio = ratio
                best_match = entity_map[entity_name]
        
        return best_match
    
    async def infer_relations_from_descriptions(self, entities: List[Entity]) -> List[Relation]:
        """从实体描述中推理关系"""
        if not settings.ENABLE_RELATION_INFERENCE:
            return []
        
        inferred_relations = []
        
        # 为每个实体分析其描述中提到的其他实体
        entity_name_map = {entity.name.lower(): entity for entity in entities}
        
        for entity in entities:
            if not entity.description:
                continue
            
            # 查找描述中提到的其他实体
            mentioned_entities = []
            for other_entity in entities:
                if other_entity.id == entity.id:
                    continue
                
                if other_entity.name.lower() in entity.description.lower():
                    mentioned_entities.append(other_entity)
            
            # 如果找到提及的实体，使用LLM推理关系
            if mentioned_entities:
                relations = await self._infer_relations_with_llm(entity, mentioned_entities)
                inferred_relations.extend(relations)
        
        logger.info(f"从描述中推理出 {len(inferred_relations)} 个关系")
        return inferred_relations
    
    async def _infer_relations_with_llm(self, source_entity: Entity, mentioned_entities: List[Entity]) -> List[Relation]:
        """使用LLM推理实体间的关系"""
        try:
            mentioned_names = [e.name for e in mentioned_entities]
            
            prompt = f"""
基于以下实体的描述，推理出该实体与其描述中提到的其他实体之间的关系。

主实体：{source_entity.name}
类型：{source_entity.type}
描述：{source_entity.description}

描述中提到的其他实体：{', '.join(mentioned_names)}

请分析并返回JSON格式的关系列表：
{{
    "relations": [
        {{
            "target": "目标实体名称",
            "relation": "关系类型",
            "description": "关系描述",
            "confidence": 0.8
        }}
    ]
}}

只返回JSON，不要其他内容。
"""
            
            messages = [
                {"role": "system", "content": "你是一个专业的关系推理专家，擅长从文本描述中识别实体间的隐含关系。"},
                {"role": "user", "content": prompt}
            ]
            
            response = await self.llm_service._call_llm(messages)
            
            # 解析JSON响应
            import json
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                data = json.loads(json_match.group())
                relations = []
                
                for rel_data in data.get("relations", []):
                    target_name = rel_data.get("target", "")
                    target_entity = None
                    
                    # 查找目标实体
                    for entity in mentioned_entities:
                        if entity.name.lower() == target_name.lower():
                            target_entity = entity
                            break
                    
                    if target_entity:
                        import uuid
                        relation = Relation(
                            id=str(uuid.uuid4()),
                            source_entity=source_entity.id,
                            target_entity=target_entity.id,
                            relation_type=rel_data.get("relation", "related"),
                            confidence=rel_data.get("confidence", 0.8),
                            properties={
                                "description": rel_data.get("description", ""),
                                "inferred_from": "entity_description"
                            }
                        )
                        relations.append(relation)
                
                return relations
        
        except Exception as e:
            logger.error(f"关系推理失败: {e}")
            return []
        
        return []
    
    def check_graph_connectivity(self, kg: KnowledgeGraph) -> Dict[str, any]:
        """检查图谱连通性"""
        if not settings.ENABLE_CONNECTIVITY_CHECK:
            return {"isolated_nodes": [], "connected_components": 1}
        
        # 转换为NetworkX图
        G = kg.to_networkx()
        
        # 分析连通性
        connected_components = list(nx.connected_components(G))
        isolated_nodes = [comp for comp in connected_components if len(comp) == 1]
        
        connectivity_info = {
            "total_nodes": len(G.nodes()),
            "total_edges": len(G.edges()),
            "connected_components": len(connected_components),
            "largest_component_size": max(len(comp) for comp in connected_components) if connected_components else 0,
            "isolated_nodes": [list(comp)[0] for comp in isolated_nodes],
            "isolated_count": len(isolated_nodes)
        }
        
        logger.info(f"图谱连通性分析: {connectivity_info['connected_components']} 个连通分量, {connectivity_info['isolated_count']} 个孤立节点")
        
        return connectivity_info
