# 知识图谱 Wiki 网页生成功能

## 🎯 功能概述

为每个知识图谱自动生成对应的 Wiki 风格网页，提供实体详细描述、关系网络分析和交互式访问。

## ✨ 新增功能

### 1. 🤖 智能 Wiki 生成
- **豆包模型驱动**: 使用 `doubao-seed-1.6` 模型生成高质量内容
- **并行处理**: 支持多实体并行描述生成，提升速度
- **自动更新**: 知识图谱更新时自动同步 Wiki 内容

### 2. 📄 丰富的 Wiki 内容
- **概述生成**: 智能分析图谱特点，生成专业概述
- **实体详情**: 为每个实体生成维基百科风格的详细描述
- **关系网络**: 分析和描述实体间的复杂关系
- **统计信息**: 提供图谱的全面统计分析

### 3. 🎨 精美的网页设计
- **响应式设计**: 适配各种设备屏幕
- **现代化UI**: 渐变色彩、卡片布局、平滑动画
- **导航便捷**: 目录导航、锚点跳转、返回图谱链接
- **可视化友好**: 为后续图谱可视化预留接口

### 4. 🔗 完整的 API 集成
- **自动生成**: 构建图谱时自动生成 Wiki
- **手动触发**: 支持单独生成和更新 Wiki
- **批量管理**: 列出、查看、更新、删除 Wiki 页面
- **内容API**: 提供 JSON 格式的 Wiki 内容访问

## 🏗️ 技术架构

### 模型配置优化
```python
# 主模型（实体关系提取）
LLM_MODEL = "qwen2.5-32b-instruct-int4"  # 优化版本，更快速度

# Wiki生成专用模型
DOUBAO_MODEL = "doubao-seed-1.6"  # 豆包模型，专门用于内容生成
WIKI_GENERATION_MODEL = "doubao-seed-1.6"
```

### 核心组件
1. **WikiGenerator**: Wiki 生成器核心类
2. **模板引擎**: Jinja2 模板系统
3. **并行处理**: 异步并发生成实体描述
4. **API集成**: 完整的 RESTful API

### 目录结构
```
├── data/
│   └── wiki_pages/          # Wiki 页面存储
│       ├── {kg_id}.html     # HTML 页面
│       └── {kg_id}.json     # JSON 数据
├── templates/
│   └── wiki/                # Wiki 模板
│       └── wiki_template.html
└── utils/
    └── wiki_generator.py    # Wiki 生成器
```

## 🚀 使用方式

### 1. 自动生成（推荐）
构建知识图谱时自动生成 Wiki：
```python
from app.simple_flow import build_kg_from_file

# 构建图谱，自动生成 Wiki
kg = await build_kg_from_file("document.pdf", "我的图谱")
# Wiki 自动生成并保存到 /wiki/{kg.id}.html
```

### 2. API 调用
```bash
# 生成 Wiki 页面
curl -X POST "http://localhost:8000/api/wiki/generate" \
     -H "Content-Type: application/json" \
     -d '{"kg_id": "your-kg-id"}'

# 获取 Wiki 列表
curl -X GET "http://localhost:8000/api/wiki/list"

# 获取 Wiki 信息
curl -X GET "http://localhost:8000/api/wiki/{kg_id}"

# 更新 Wiki 页面
curl -X PUT "http://localhost:8000/api/wiki/{kg_id}/update"
```

### 3. 直接访问
```
# HTML 页面
http://localhost:8000/wiki/{kg_id}.html

# JSON 内容
http://localhost:8000/api/wiki/{kg_id}/content
```

## 📊 Wiki 页面内容

### 页面结构
1. **标题头部**: 图谱名称、生成时间、导航链接
2. **目录导航**: 快速跳转到各个章节
3. **概述部分**: 图谱整体介绍和特点分析
4. **统计信息**: 实体数量、关系数量、网络密度等
5. **实体详情**: 每个实体的详细描述
6. **关系网络**: 关系分析和主要连接
7. **可视化入口**: 跳转到交互式图谱

### 内容特色
- **AI 生成**: 使用豆包模型生成专业内容
- **结构化**: 清晰的章节组织和层次结构
- **交互性**: 平滑滚动、悬停效果、点击跳转
- **实时性**: 随图谱更新自动刷新内容

## ⚙️ 配置选项

### 环境变量配置
```env
# 启用 Wiki 生成
ENABLE_WIKI_GENERATION=true

# 豆包模型配置
DOUBAO_MODEL=doubao-seed-1.6
DOUBAO_MAX_TOKENS=8000
DOUBAO_TEMPERATURE=0.3

# Wiki 生成配置
WIKI_PARALLEL_PROCESSING=true
WIKI_MAX_CONCURRENT=5
WIKI_AUTO_UPDATE=true
```

### 性能优化
- **并行处理**: 多实体描述并行生成
- **模型优化**: 使用 int4 量化版本提升速度
- **缓存机制**: 避免重复生成相同内容
- **异步处理**: 不阻塞主要图谱构建流程

## 🔄 集成流程

### 知识图谱构建流程
```
1. 文件上传 → 2. 文本提取 → 3. 实体关系提取 → 4. 图谱构建 → 5. 图谱保存 → 6. Wiki生成 ✨
```

### Wiki 更新流程
```
图谱更新 → 检测变化 → 重新生成Wiki → 更新HTML/JSON → 通知前端
```

## 🎨 前端集成

### 图谱查看界面增强
- **Wiki 链接**: 在图谱查看页面显示 Wiki 入口
- **预览功能**: 悬停显示 Wiki 概述
- **无缝切换**: 图谱可视化与 Wiki 页面间的流畅切换

### 用户体验
- **一键访问**: 从图谱直接跳转到 Wiki
- **移动友好**: 响应式设计适配手机平板
- **加载优化**: 快速加载和渲染

## 📈 性能指标

### 生成速度
- **单实体描述**: ~2-3秒（豆包模型）
- **完整Wiki页面**: ~10-30秒（取决于实体数量）
- **并行处理**: 5个实体并发，显著提升速度

### 内容质量
- **专业性**: 维基百科风格的客观描述
- **准确性**: 基于图谱数据的事实性内容
- **丰富性**: 300-800字的详细实体描述

## 🔮 未来扩展

### 计划功能
1. **多语言支持**: 生成多语言版本的 Wiki
2. **模板定制**: 支持自定义 Wiki 模板
3. **图片集成**: 自动获取和展示相关图片
4. **版本历史**: 追踪 Wiki 内容的变更历史
5. **协作编辑**: 支持用户手动编辑和改进内容

### 技术优化
1. **缓存策略**: 智能缓存减少重复生成
2. **增量更新**: 只更新变化的部分
3. **搜索功能**: Wiki 内容全文搜索
4. **导出功能**: 支持 PDF、Word 等格式导出

## 🎉 总结

Wiki 功能为知识图谱系统带来了：
- ✅ **内容丰富化**: 从图谱到详细文档的完整体验
- ✅ **用户友好**: 直观的网页界面和专业内容
- ✅ **自动化**: 无需手动维护，自动生成和更新
- ✅ **高性能**: 豆包模型并行处理，快速生成
- ✅ **可扩展**: 模块化设计，易于扩展新功能

现在用户可以：
1. 🚀 构建知识图谱时自动获得专业 Wiki 页面
2. 🔍 通过 Wiki 深入了解实体详情和关系网络
3. 🌐 在图谱可视化和文档阅读间无缝切换
4. 📱 在任何设备上访问美观的知识图谱文档

这个功能将知识图谱从纯粹的数据结构转变为丰富的知识文档，大大提升了用户体验和实用价值！
