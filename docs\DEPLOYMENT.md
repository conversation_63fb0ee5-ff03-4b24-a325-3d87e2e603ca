# 知识图谱构建系统部署指南

## 系统要求

### 硬件要求
- CPU: 4核心以上
- 内存: 8GB以上
- 存储: 20GB以上可用空间
- 网络: 稳定的互联网连接（用于访问LLM API）

### 软件要求
- Python 3.8+
- Node.js 16+
- npm 或 yarn

## 快速部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd knowledge-graph-system
```

### 2. 后端部署

#### 安装Python依赖
```bash
cd backend
pip install -r requirements.txt
```

#### 配置环境变量
复制 `.env` 文件并根据需要修改配置：
```bash
cp .env.example .env
```

主要配置项：
- `LLM_API_KEY`: 您的LLM API密钥
- `LLM_API_BASE`: LLM API地址
- `HOST`: 服务监听地址
- `PORT`: 服务端口

#### 启动后端服务
```bash
python main.py
```

或使用启动脚本：
```bash
# Windows
start_backend.bat

# Linux/Mac
chmod +x start_backend.sh
./start_backend.sh
```

### 3. 前端部署

#### 安装Node.js依赖
```bash
cd frontend
npm install
```

#### 启动开发服务器
```bash
npm run dev
```

或使用启动脚本：
```bash
# Windows
start_frontend.bat

# Linux/Mac
chmod +x start_frontend.sh
./start_frontend.sh
```

#### 构建生产版本
```bash
npm run build
```

## 生产环境部署

### 使用Docker部署

#### 1. 构建Docker镜像
```bash
# 后端
docker build -t kg-backend ./backend

# 前端
docker build -t kg-frontend ./frontend
```

#### 2. 使用Docker Compose
```bash
docker-compose up -d
```

### 使用Nginx反向代理

#### Nginx配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        root /path/to/frontend/dist;
        try_files $uri $uri/ /index.html;
    }

    # 后端API
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 配置说明

### 后端配置 (.env)

```env
# 应用基本配置
APP_NAME=知识图谱构建系统
DEBUG=false
HOST=0.0.0.0
PORT=8000

# LLM API配置
LLM_API_BASE=https://gateway.chat.sensedeal.vip/v1
LLM_API_KEY=your-api-key
LLM_MODEL=qwen2.5-32b-instruct
LLM_MAX_TOKENS=4000
LLM_TEMPERATURE=0.1

# 文件处理配置
MAX_FILE_SIZE=104857600  # 100MB
CHUNK_SIZE=2000
CHUNK_OVERLAP=200

# 知识图谱配置
MAX_ENTITIES_PER_CHUNK=50
MAX_RELATIONS_PER_CHUNK=100
```

### 前端配置

修改 `frontend/vite.config.ts` 中的代理配置：
```typescript
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'http://your-backend-url:8000',
        changeOrigin: true,
      }
    }
  }
})
```

## 测试部署

### 1. 运行API测试
```bash
cd backend
python test_api.py
```

### 2. 检查服务状态
- 后端健康检查: `http://localhost:8000/health`
- 前端访问: `http://localhost:3000`
- API文档: `http://localhost:8000/docs`

## 常见问题

### 1. LLM API连接失败
- 检查API密钥是否正确
- 确认网络连接正常
- 验证API地址是否可访问

### 2. 文件上传失败
- 检查文件大小是否超过限制
- 确认文件格式是否支持
- 检查磁盘空间是否充足

### 3. 前端无法访问后端
- 确认后端服务已启动
- 检查端口是否被占用
- 验证CORS配置是否正确

### 4. 内存不足
- 减少文本块大小 (CHUNK_SIZE)
- 降低并发处理数量
- 增加系统内存

## 性能优化

### 1. 后端优化
- 使用异步处理提高并发性能
- 实现文件处理队列
- 添加Redis缓存
- 使用数据库存储图谱数据

### 2. 前端优化
- 启用代码分割
- 使用CDN加速静态资源
- 实现虚拟滚动优化大列表
- 添加图谱数据缓存

### 3. 系统优化
- 使用负载均衡
- 实现水平扩展
- 添加监控和日志
- 定期清理临时文件

## 监控和维护

### 1. 日志监控
- 应用日志: `logs/app.log`
- 错误日志: 控制台输出
- 访问日志: Nginx日志

### 2. 性能监控
- CPU和内存使用率
- API响应时间
- 文件处理队列长度
- 图谱构建成功率

### 3. 定期维护
- 清理过期文件
- 备份知识图谱数据
- 更新依赖包
- 检查安全漏洞

## 安全建议

1. 使用HTTPS加密传输
2. 设置API访问限制
3. 定期更新依赖包
4. 实现用户认证和授权
5. 限制文件上传类型和大小
6. 定期备份重要数据
