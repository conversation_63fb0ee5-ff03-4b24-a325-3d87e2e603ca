"""
知识图谱服务
"""
import json
import uuid
import time
from typing import List, Dict, Any, Optional, Set, Tuple
from pathlib import Path
import networkx as nx
from datetime import datetime

from app.core.config import settings
from app.models.knowledge_graph import (
    Entity, Relation, KnowledgeGraph, GraphSearchResult, GraphStats
)
from app.models.file_models import TextChunk
from app.services.llm_service import LLMService
from app.services.progress_tracker import progress_tracker, TaskStatus
from app.services.entity_disambiguation_service import EntityDisambiguationService
from app.services.entity_preprocessing_service import EntityPreprocessingService
from loguru import logger

class KnowledgeGraphService:
    """知识图谱服务类"""
    
    def __init__(self):
        self.kg_data_dir = Path(settings.KG_DATA_DIR)
        self.kg_data_dir.mkdir(exist_ok=True)
        self.llm_service = LLMService()
        self.graphs: Dict[str, KnowledgeGraph] = {}
        self.disambiguation_service = EntityDisambiguationService(self.llm_service)
        self.preprocessing_service = EntityPreprocessingService()
        # 导入文件服务用于临时文件管理
        from app.services.file_service import FileService
        self.file_service = FileService()
    
    async def build_knowledge_graph_from_chunks(
        self,
        chunks: List[TextChunk],
        kg_name: str = "default",
        progress_callback=None,
        task_id: str = None
    ) -> KnowledgeGraph:
        """从文本块构建知识图谱（支持并行处理）"""
        kg_id = str(uuid.uuid4())
        all_entities = []
        all_relations = []

        # 处理所有文本块
        chunks_to_process = chunks
        total_chunks = len(chunks)

        logger.info(f"开始构建知识图谱，共 {total_chunks} 个文本块")

        # 清空实体预处理服务的全局缓存（新文档处理）
        self.preprocessing_service.clear_global_cache()

        # 创建进度跟踪任务
        if task_id is None:
            task_id = f"kg_build_{kg_id[:8]}"

        await progress_tracker.create_task(
            task_id=task_id,
            task_name=f"构建知识图谱: {kg_name}",
            total=total_chunks
        )
        await progress_tracker.start_task(task_id)

        if settings.ENABLE_PARALLEL_PROCESSING and total_chunks > 1:
            # 并行处理模式
            logger.info(f"使用并行处理模式，最大并发数: {settings.MAX_CONCURRENT_CHUNKS}")

            # 准备文本内容列表
            texts = [chunk.content for chunk in chunks_to_process]

            # 创建进度回调函数
            def progress_wrapper(completed: int, total: int):
                logger.info(f"处理进度: {completed}/{total}")
                # 异步更新进度（在后台执行）
                import asyncio
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        # 如果事件循环正在运行，创建任务
                        loop.create_task(progress_tracker.update_progress(task_id, completed))
                    else:
                        # 如果没有运行的事件循环，直接运行
                        asyncio.run(progress_tracker.update_progress(task_id, completed))
                except Exception as e:
                    logger.warning(f"更新进度失败: {e}")

                if progress_callback:
                    progress_callback(completed, total)

            # 批量并发处理
            results = await self.llm_service.extract_entities_and_relations_batch(
                texts,
                progress_callback=progress_wrapper
            )

            # 使用实体预处理服务处理批量结果
            logger.info("开始实体预处理...")
            preprocessed_entities, preprocessed_relations = await self.preprocessing_service.preprocess_entities_batch(
                results, texts, kg_name
            )

            # 为预处理后的实体和关系添加来源信息
            for i, (entities, relations) in enumerate(results):
                chunk = chunks_to_process[i]

                # 为原始实体和关系添加来源信息（用于调试）
                for entity in entities:
                    entity.properties["source_chunk"] = chunk.id

                for relation in relations:
                    relation.properties["source_chunk"] = chunk.id

            # 为预处理后的实体和关系添加来源信息
            for entity in preprocessed_entities:
                if "source_chunk" not in entity.properties:
                    entity.properties["source_chunk"] = "preprocessed"

            for relation in preprocessed_relations:
                if "source_chunk" not in relation.properties:
                    relation.properties["source_chunk"] = "preprocessed"

            all_entities.extend(preprocessed_entities)
            all_relations.extend(preprocessed_relations)
        else:
            # 串行处理模式（兼容性保持）
            logger.info("使用串行处理模式")
            for i, chunk in enumerate(chunks_to_process):
                try:
                    logger.info(f"处理文本块 {i+1}/{total_chunks}")
                    entities, relations = await self.llm_service.extract_entities_and_relations(chunk.content)

                    # 使用实体预处理服务处理单个文本块结果
                    preprocessed_entities, preprocessed_relations = await self.preprocessing_service.preprocess_single_extraction(
                        entities, relations, chunk.content, chunk.id
                    )

                    # 为实体和关系添加来源信息
                    for entity in preprocessed_entities:
                        entity.properties["source_chunk"] = chunk.id

                    for relation in preprocessed_relations:
                        relation.properties["source_chunk"] = chunk.id

                    all_entities.extend(preprocessed_entities)
                    all_relations.extend(preprocessed_relations)

                    # 更新进度
                    await progress_tracker.update_progress(task_id, i + 1)
                    if progress_callback:
                        progress_callback(i + 1, total_chunks)

                except Exception as e:
                    logger.error(f"处理文本块 {chunk.id} 失败: {e}")
                    continue
        
        try:
            # 使用增强的实体消歧和合并
            logger.info("开始实体消歧和合并...")
            merged_entities, id_mapping, unmatched_relations = await self._enhanced_entity_disambiguation(all_entities, all_relations)
            logger.info(f"实体消歧完成，合并后实体数: {len(merged_entities)}")

            # 更新关系中的实体ID
            logger.info("开始更新关系中的实体ID...")
            updated_relations = self._update_relation_entity_ids_with_mapping(all_relations, merged_entities, id_mapping)
            logger.info(f"关系ID更新完成，有效关系数: {len(updated_relations)}")

            # 处理未匹配的关系
            if unmatched_relations:
                logger.info(f"处理 {len(unmatched_relations)} 个未匹配关系...")
                additional_relations = self.disambiguation_service.fuzzy_match_entities(unmatched_relations, merged_entities)
                updated_relations.extend(additional_relations)
                logger.info(f"模糊匹配添加了 {len(additional_relations)} 个关系")

            # 从实体描述中推理额外关系
            if settings.ENABLE_RELATION_INFERENCE:
                logger.info("开始从实体描述推理关系...")
                inferred_relations = await self.disambiguation_service.infer_relations_from_descriptions(merged_entities)
                updated_relations.extend(inferred_relations)
                logger.info(f"关系推理添加了 {len(inferred_relations)} 个关系")
        except Exception as e:
            logger.error(f"实体消歧和关系处理过程中出错: {e}")
            logger.exception("详细错误信息:")
            # 使用原始数据继续处理
            merged_entities = all_entities
            updated_relations = all_relations
        
        # 创建知识图谱
        logger.info("开始创建知识图谱对象...")
        kg = KnowledgeGraph(
            id=kg_id,
            name=kg_name,
            entities=merged_entities,
            relations=updated_relations,
            metadata={
                "total_chunks": len(chunks),
                "creation_method": "llm_extraction"
            }
        )
        logger.info(f"知识图谱对象创建完成: {len(merged_entities)} 个实体, {len(updated_relations)} 个关系")

        # 检查图谱连通性
        logger.info("开始检查图谱连通性...")
        connectivity_info = self.disambiguation_service.check_graph_connectivity(kg)
        logger.info(f"连通性检查完成: {connectivity_info['connected_components']} 个连通分量, {connectivity_info['isolated_count']} 个孤立节点")

        # 如果有孤立节点，尝试修复
        if connectivity_info["isolated_count"] > 0:
            logger.warning(f"发现 {connectivity_info['isolated_count']} 个孤立节点，尝试修复...")
            try:
                additional_relations = await self._fix_isolated_nodes(kg, connectivity_info["isolated_nodes"])
                if additional_relations:
                    kg.relations.extend(additional_relations)
                    logger.info(f"通过关系推理添加了 {len(additional_relations)} 个关系")
                else:
                    logger.info("未能通过关系推理修复孤立节点")
            except Exception as e:
                logger.error(f"修复孤立节点时出错: {e}")

        # 保存图谱
        logger.info("开始保存知识图谱...")
        try:
            await self.save_knowledge_graph(kg)
            self.graphs[kg_id] = kg
            logger.info("知识图谱保存成功")
        except Exception as e:
            logger.error(f"保存知识图谱失败: {e}")
            await progress_tracker.complete_task(task_id, success=False, error=str(e))
            raise

        # 完成进度跟踪
        logger.info("完成进度跟踪...")
        await progress_tracker.complete_task(task_id, success=True)

        # 最终连通性报告
        final_connectivity = self.disambiguation_service.check_graph_connectivity(kg)
        logger.info(f"知识图谱构建完成: {len(merged_entities)} 个实体, {len(kg.relations)} 个关系")
        logger.info(f"连通性: {final_connectivity['connected_components']} 个连通分量, {final_connectivity['isolated_count']} 个孤立节点")

        return kg

    async def build_knowledge_graph_with_preprocessing(
        self,
        text: str,
        filename: str = "",
        kg_name: str = "default",
        task_id: str = None
    ) -> KnowledgeGraph:
        """使用预处理构建知识图谱"""
        kg_id = str(uuid.uuid4())

        logger.info(f"开始预处理文本: {filename}")

        # 使用LLM预处理文本
        preprocessed_text = await self.llm_service.preprocess_text_for_kg(text, filename)

        # 创建临时文件
        temp_file_path = await self.file_service.create_temp_file(
            preprocessed_text,
            f"preprocessed_{kg_id}"
        )

        try:
            # 分割预处理后的文本
            chunks = self.file_service.split_text_into_chunks(preprocessed_text)

            # 构建知识图谱
            kg = await self.build_knowledge_graph_from_chunks(chunks, kg_name, task_id=task_id)

            # 更新元数据
            kg.metadata.update({
                "preprocessing_used": True,
                "original_filename": filename,
                "temp_file_path": temp_file_path
            })

            # 保存更新后的图谱
            await self.save_knowledge_graph(kg)

            return kg

        finally:
            # 清理临时文件
            await self.file_service.delete_file(temp_file_path)

    async def build_knowledge_graph_from_multiple_files(
        self,
        texts: List[str],
        filenames: List[str] = None,
        kg_name: str = "batch_processed"
    ) -> KnowledgeGraph:
        """从多个文件构建统一的知识图谱（渐进式构建）"""
        if filenames is None:
            filenames = [f"文件{i+1}" for i in range(len(texts))]

        if len(texts) == 0:
            raise ValueError("没有提供任何文本内容")

        if len(texts) == 1:
            # 单文件直接构建
            return await self.build_knowledge_graph_with_preprocessing(
                texts[0], filenames[0], kg_name
            )

        # 创建主进度跟踪任务
        main_task_id = f"batch_build_{kg_name}_{int(time.time())}"
        total_steps = len(texts) + 1  # 文件数量 + 最终优化步骤

        await progress_tracker.create_task(
            task_id=main_task_id,
            task_name=f"批量构建知识图谱: {kg_name}",
            total=total_steps
        )
        await progress_tracker.start_task(main_task_id)

        logger.info(f"开始渐进式构建知识图谱，共 {len(texts)} 个文件")

        successful_files = []
        failed_files = []
        current_kg = None

        try:
            # 第一步：从第一个文件构建基础图谱
            logger.info(f"第一步：从文件 '{filenames[0]}' 构建基础图谱")
            await progress_tracker.update_progress(
                main_task_id, 0, f"构建基础图谱: {filenames[0]}"
            )

            try:
                base_kg = await self.build_knowledge_graph_with_preprocessing(
                    texts[0], filenames[0], f"{kg_name}_base"
                )
                logger.info(f"基础图谱构建完成: {len(base_kg.entities)} 个实体, {len(base_kg.relations)} 个关系")
                successful_files.append(filenames[0])
                current_kg = base_kg

                await progress_tracker.update_progress(
                    main_task_id, 1, f"基础图谱构建完成: {len(base_kg.entities)} 实体"
                )

            except Exception as e:
                logger.error(f"基础图谱构建失败: {e}")
                failed_files.append((filenames[0], str(e)))
                # 如果第一个文件失败，尝试用第二个文件作为基础
                if len(texts) > 1:
                    logger.info(f"尝试使用文件 '{filenames[1]}' 作为基础图谱")
                    try:
                        base_kg = await self.build_knowledge_graph_with_preprocessing(
                            texts[1], filenames[1], f"{kg_name}_base"
                        )
                        successful_files.append(filenames[1])
                        current_kg = base_kg
                        await progress_tracker.update_progress(
                            main_task_id, 1, f"备用基础图谱构建完成: {len(base_kg.entities)} 实体"
                        )
                    except Exception as e2:
                        logger.error(f"备用基础图谱也构建失败: {e2}")
                        failed_files.append((filenames[1], str(e2)))
                        raise ValueError("无法构建基础知识图谱")

            # 第二步：逐个添加其他文件内容
            start_index = 2 if len(failed_files) > 0 and len(successful_files) > 0 else 1

            for i in range(start_index, len(texts)):
                step_num = i + 1
                logger.info(f"第{step_num}步：添加文件 '{filenames[i]}' 到图谱中")

                await progress_tracker.update_progress(
                    main_task_id, step_num - 1, f"合并文件: {filenames[i]}"
                )

                try:
                    current_kg = await self._merge_file_into_kg(
                        current_kg, texts[i], filenames[i], f"{kg_name}_step_{step_num}"
                    )
                    logger.info(f"步骤{step_num}完成: {len(current_kg.entities)} 个实体, {len(current_kg.relations)} 个关系")
                    successful_files.append(filenames[i])

                    await progress_tracker.update_progress(
                        main_task_id, step_num, f"文件合并完成: {len(current_kg.entities)} 实体"
                    )

                except Exception as e:
                    logger.error(f"文件 '{filenames[i]}' 合并失败: {e}")
                    failed_files.append((filenames[i], str(e)))
                    # 继续处理下一个文件，不中断整个流程
                    continue

            # 检查是否有成功处理的文件
            if not current_kg or len(successful_files) == 0:
                raise ValueError("没有成功处理任何文件")

            # 第三步：最终优化和清理
            logger.info("第三步：最终优化和清理")
            await progress_tracker.update_progress(
                main_task_id, total_steps - 1, "最终优化和清理"
            )

            final_kg = await self._finalize_merged_kg(current_kg, kg_name, successful_files)

            await progress_tracker.update_progress(
                main_task_id, total_steps, "构建完成"
            )

            # 记录处理结果
            success_msg = f"渐进式构建完成: 最终图谱包含 {len(final_kg.entities)} 个实体, {len(final_kg.relations)} 个关系"
            if failed_files:
                success_msg += f" (成功: {len(successful_files)}, 失败: {len(failed_files)})"
            logger.info(success_msg)

            # 在元数据中记录处理结果
            final_kg.metadata.update({
                "successful_files": successful_files,
                "failed_files": [{"filename": f, "error": e} for f, e in failed_files],
                "success_rate": len(successful_files) / len(texts)
            })

            await progress_tracker.complete_task(main_task_id, True)
            return final_kg

        except Exception as e:
            logger.error(f"渐进式构建失败: {e}")
            await progress_tracker.complete_task(main_task_id, False, str(e))

            # 回退到原有的整合方法
            logger.info("回退到原有的文本整合方法")
            return await self._fallback_build_from_multiple_files(texts, filenames, kg_name)

    async def _merge_file_into_kg(
        self,
        existing_kg: KnowledgeGraph,
        new_text: str,
        new_filename: str,
        temp_kg_name: str
    ) -> KnowledgeGraph:
        """将新文件内容合并到现有知识图谱中（带进度跟踪和错误处理）"""
        merge_task_id = f"merge_{new_filename}_{int(time.time())}"

        await progress_tracker.create_task(
            task_id=merge_task_id,
            task_name=f"合并文件: {new_filename}",
            total=4  # 构建新图谱、合并实体、更新关系、增强关系
        )
        await progress_tracker.start_task(merge_task_id)

        logger.info(f"开始将文件 '{new_filename}' 合并到现有图谱中")

        try:
            # 步骤1：从新文件构建临时知识图谱
            await progress_tracker.update_progress(merge_task_id, 0, "构建新文件图谱")

            try:
                new_kg = await self.build_knowledge_graph_with_preprocessing(
                    new_text, new_filename, temp_kg_name
                )
                logger.info(f"新文件图谱构建完成: {len(new_kg.entities)} 个实体, {len(new_kg.relations)} 个关系")
                await progress_tracker.update_progress(merge_task_id, 1, f"新图谱构建完成: {len(new_kg.entities)} 实体")

            except Exception as e:
                logger.error(f"新文件图谱构建失败: {e}")
                await progress_tracker.complete_task(merge_task_id, False, f"图谱构建失败: {str(e)}")
                raise

            # 步骤2：合并两个图谱
            await progress_tracker.update_progress(merge_task_id, 1, "开始合并图谱")

            merged_entities = existing_kg.entities + new_kg.entities
            merged_relations = existing_kg.relations + new_kg.relations

            logger.info(f"开始智能合并: 原图谱({len(existing_kg.entities)}实体, {len(existing_kg.relations)}关系) + 新图谱({len(new_kg.entities)}实体, {len(new_kg.relations)}关系)")

            # 步骤3：使用增强的实体合并和关系处理
            merge_success = True
            merge_error = None

            try:
                # 智能合并重复实体
                await progress_tracker.update_progress(merge_task_id, 2, "合并重复实体")
                merged_entities = self._merge_duplicate_entities(merged_entities)
                logger.info(f"实体合并完成，合并后实体数: {len(merged_entities)}")

                # 更新关系中的实体ID
                await progress_tracker.update_progress(merge_task_id, 3, "更新关系ID")
                updated_relations = self._update_relation_entity_ids(merged_relations, merged_entities)
                logger.info(f"关系ID更新完成，有效关系数: {len(updated_relations)}")

                # 增强关系发现
                await progress_tracker.update_progress(merge_task_id, 3, "增强关系发现")
                enhanced_relations = await self._enhance_relations_after_merge(merged_entities, updated_relations)
                logger.info(f"关系增强完成，最终关系数: {len(enhanced_relations)}")

                merged_relations = enhanced_relations

            except Exception as e:
                logger.error(f"智能合并处理失败: {e}")
                merge_success = False
                merge_error = str(e)

                # 回退到简单合并
                logger.info("回退到简单合并策略")
                try:
                    merged_entities = self._merge_duplicate_entities(merged_entities)
                    merged_relations = self._update_relation_entity_ids(merged_relations, merged_entities)
                    logger.info(f"简单合并完成: {len(merged_entities)} 实体, {len(merged_relations)} 关系")
                except Exception as e2:
                    logger.error(f"简单合并也失败: {e2}")
                    await progress_tracker.complete_task(merge_task_id, False, f"合并失败: {str(e2)}")
                    raise

            # 步骤4：创建合并后的图谱
            await progress_tracker.update_progress(merge_task_id, 4, "创建最终图谱")

            merged_kg = KnowledgeGraph(
                id=str(uuid.uuid4()),
                name=f"{existing_kg.name}_merged_{new_filename}",
                entities=merged_entities,
                relations=merged_relations,
                metadata={
                    "progressive_build": True,
                    "base_kg_id": existing_kg.id,
                    "added_file": new_filename,
                    "merge_step": True,
                    "entities_before": len(existing_kg.entities),
                    "entities_after": len(merged_entities),
                    "relations_before": len(existing_kg.relations),
                    "relations_after": len(merged_relations),
                    "merge_success": merge_success,
                    "merge_error": merge_error,
                    "merge_strategy": "enhanced" if merge_success else "simple"
                }
            )

            await progress_tracker.complete_task(merge_task_id, True)
            return merged_kg

        except Exception as e:
            logger.error(f"文件合并过程失败: {e}")
            await progress_tracker.complete_task(merge_task_id, False, str(e))
            raise

    async def _finalize_merged_kg(
        self,
        kg: KnowledgeGraph,
        final_name: str,
        source_filenames: List[str]
    ) -> KnowledgeGraph:
        """最终优化合并后的知识图谱"""
        logger.info("开始最终优化合并后的知识图谱")

        # 最终的实体去重和优化
        final_entities = self._merge_duplicate_entities(kg.entities)
        final_relations = self._update_relation_entity_ids(kg.relations, final_entities)

        # 创建最终图谱
        final_kg = KnowledgeGraph(
            id=str(uuid.uuid4()),
            name=final_name,
            entities=final_entities,
            relations=final_relations,
            metadata={
                "batch_processing": True,
                "progressive_build": True,
                "source_files": source_filenames,
                "file_count": len(source_filenames),
                "final_entities_count": len(final_entities),
                "final_relations_count": len(final_relations),
                "build_method": "progressive_merge"
            }
        )

        # 保存最终图谱
        await self.save_knowledge_graph(final_kg)

        # 成功生成图谱后清理上传文件
        try:
            await self._cleanup_after_success(final_kg.id, [])
        except Exception as e:
            logger.warning(f"清理文件时出错: {e}")

        return final_kg

    async def _fallback_build_from_multiple_files(
        self,
        texts: List[str],
        filenames: List[str],
        kg_name: str
    ) -> KnowledgeGraph:
        """回退到原有的文本整合方法"""
        kg_id = str(uuid.uuid4())

        logger.info(f"使用回退方法整合 {len(texts)} 个文件的内容")

        # 使用LLM整合多个文本
        merged_text = await self.llm_service.merge_multiple_texts_for_kg(texts, filenames)

        # 创建临时文件
        temp_file_path = await self.file_service.create_temp_file(
            merged_text,
            f"fallback_merged_{kg_id}"
        )

        try:
            # 分割整合后的文本
            chunks = self.file_service.split_text_into_chunks(merged_text)

            # 构建知识图谱
            kg = await self.build_knowledge_graph_from_chunks(chunks, kg_name)

            # 更新元数据
            kg.metadata.update({
                "batch_processing": True,
                "source_files": filenames,
                "file_count": len(texts),
                "build_method": "fallback_merge",
                "temp_file_path": temp_file_path
            })

            # 保存更新后的图谱
            await self.save_knowledge_graph(kg)

            # 成功生成图谱后清理上传文件
            await self._cleanup_after_success(kg_id, [])

            return kg

        finally:
            # 清理临时文件
            await self.file_service.delete_file(temp_file_path)
    
    def _merge_duplicate_entities(self, entities: List[Entity]) -> List[Entity]:
        """智能合并重复实体，增强实体信息和关系（多文件优化版）"""
        from loguru import logger

        logger.info(f"开始智能合并 {len(entities)} 个实体...")

        # 1. 预处理：按类型分组，避免不同类型实体误合并
        type_groups = {}
        for entity in entities:
            entity_type = entity.type or "unknown"
            if entity_type not in type_groups:
                type_groups[entity_type] = []
            type_groups[entity_type].append(entity)

        merged_entities = []
        total_merge_stats = {"groups_merged": 0, "entities_before": len(entities), "entities_after": 0}

        # 2. 对每个类型分别进行合并
        for entity_type, type_entities in type_groups.items():
            logger.info(f"处理类型 '{entity_type}' 的 {len(type_entities)} 个实体")

            # 按名称分组实体
            entity_groups = {}
            for entity in type_entities:
                # 使用增强的标准化方法
                key = self._normalize_entity_name_enhanced(entity.name)
                if key not in entity_groups:
                    entity_groups[key] = []
                entity_groups[key].append(entity)

            # 使用语义相似度进一步合并
            merged_groups = self._merge_similar_entity_groups_enhanced(entity_groups)

            # 合并每个组内的实体
            type_merge_stats = {"groups_merged": 0, "entities_before": len(type_entities), "entities_after": 0}

            for group_key, group_entities in merged_groups.items():
                if len(group_entities) > 1:
                    type_merge_stats["groups_merged"] += 1
                    total_merge_stats["groups_merged"] += 1
                    logger.debug(f"合并实体组 '{group_key}': {[e.name for e in group_entities]}")

                merged_entity = self._merge_entity_group_enhanced(group_entities)
                merged_entities.append(merged_entity)

            type_merge_stats["entities_after"] = len(merged_groups)
            logger.info(f"类型 '{entity_type}' 合并完成: {type_merge_stats['entities_before']} -> {type_merge_stats['entities_after']} "
                       f"(合并了 {type_merge_stats['groups_merged']} 个组)")

        total_merge_stats["entities_after"] = len(merged_entities)
        logger.info(f"实体合并完成: {total_merge_stats['entities_before']} -> {total_merge_stats['entities_after']} "
                   f"(合并了 {total_merge_stats['groups_merged']} 个组)")

        return merged_entities

    def _normalize_entity_name(self, name: str) -> str:
        """标准化实体名称用于合并"""
        # 转小写，去除空格和标点
        import re
        normalized = re.sub(r'[^\w\u4e00-\u9fff]', '', name.lower().strip())
        return normalized

    def _normalize_entity_name_enhanced(self, name: str) -> str:
        """增强的实体名称标准化（多文件优化）"""
        import re

        # 1. 基础清理
        name = name.lower().strip()

        # 2. 处理常见的变体形式
        # 去除括号内容 (如: "公司(有限责任)" -> "公司")
        name = re.sub(r'\([^)]*\)', '', name)

        # 去除常见的后缀词
        suffixes = ['公司', '有限公司', '股份有限公司', 'ltd', 'inc', 'corp', '集团']
        for suffix in suffixes:
            if name.endswith(suffix):
                name = name[:-len(suffix)].strip()
                break

        # 3. 标准化空格和标点
        name = re.sub(r'[^\w\u4e00-\u9fff]', '', name)

        # 4. 处理数字和英文的标准化
        name = re.sub(r'\s+', '', name)  # 去除所有空格

        return name if name else "unknown"

    def _merge_similar_entity_groups(self, entity_groups: Dict[str, List[Entity]]) -> Dict[str, List[Entity]]:
        """使用相似度进一步合并实体组"""
        from loguru import logger

        group_keys = list(entity_groups.keys())
        merged_groups = {}
        processed_keys = set()

        for i, key1 in enumerate(group_keys):
            if key1 in processed_keys:
                continue

            # 创建新的合并组
            merged_group = entity_groups[key1].copy()
            processed_keys.add(key1)

            # 查找相似的组进行合并
            for j, key2 in enumerate(group_keys[i+1:], i+1):
                if key2 in processed_keys:
                    continue

                # 计算组间相似度
                similarity = self._calculate_group_similarity(entity_groups[key1], entity_groups[key2])

                if similarity >= 0.8:  # 高相似度阈值
                    logger.debug(f"合并相似实体组: '{key1}' 和 '{key2}' (相似度: {similarity:.3f})")
                    merged_group.extend(entity_groups[key2])
                    processed_keys.add(key2)

            merged_groups[key1] = merged_group

        return merged_groups

    def _merge_similar_entity_groups_enhanced(self, entity_groups: Dict[str, List[Entity]]) -> Dict[str, List[Entity]]:
        """增强的相似度实体组合并（多文件优化）"""
        from loguru import logger

        group_keys = list(entity_groups.keys())
        merged_groups = {}
        processed_keys = set()

        for i, key1 in enumerate(group_keys):
            if key1 in processed_keys:
                continue

            # 创建新的合并组
            merged_group = entity_groups[key1].copy()
            processed_keys.add(key1)

            # 查找相似的组进行合并
            for j, key2 in enumerate(group_keys[i+1:], i+1):
                if key2 in processed_keys:
                    continue

                # 使用多维度相似度计算
                similarity = self._calculate_group_similarity_enhanced(entity_groups[key1], entity_groups[key2])

                # 动态阈值：根据实体信息丰富程度调整
                threshold = self._get_dynamic_similarity_threshold(entity_groups[key1], entity_groups[key2])

                if similarity >= threshold:
                    logger.debug(f"合并相似实体组: '{key1}' 和 '{key2}' (相似度: {similarity:.3f}, 阈值: {threshold:.3f})")
                    merged_group.extend(entity_groups[key2])
                    processed_keys.add(key2)

            merged_groups[key1] = merged_group

        return merged_groups

    def _calculate_group_similarity(self, group1: List[Entity], group2: List[Entity]) -> float:
        """计算两个实体组的相似度"""
        # 使用组内实体名称的最大相似度
        max_similarity = 0.0

        for entity1 in group1:
            for entity2 in group2:
                similarity = self.disambiguation_service.calculate_entity_similarity(entity1, entity2)
                max_similarity = max(max_similarity, similarity)

        return max_similarity

    def _calculate_group_similarity_enhanced(self, group1: List[Entity], group2: List[Entity]) -> float:
        """增强的实体组相似度计算（多维度）"""
        # 1. 名称相似度（权重：0.4）
        name_similarity = 0.0
        for entity1 in group1:
            for entity2 in group2:
                name_sim = self._calculate_name_similarity(entity1.name, entity2.name)
                name_similarity = max(name_similarity, name_sim)

        # 2. 描述相似度（权重：0.3）
        desc_similarity = 0.0
        for entity1 in group1:
            for entity2 in group2:
                if entity1.description and entity2.description:
                    desc_sim = self._calculate_description_similarity(entity1.description, entity2.description)
                    desc_similarity = max(desc_similarity, desc_sim)

        # 3. 类型匹配度（权重：0.2）
        type_similarity = 0.0
        for entity1 in group1:
            for entity2 in group2:
                if entity1.type and entity2.type:
                    type_sim = 1.0 if entity1.type == entity2.type else 0.0
                    type_similarity = max(type_similarity, type_sim)

        # 4. 属性相似度（权重：0.1）
        prop_similarity = 0.0
        for entity1 in group1:
            for entity2 in group2:
                prop_sim = self._calculate_properties_similarity(entity1.properties, entity2.properties)
                prop_similarity = max(prop_similarity, prop_sim)

        # 加权平均
        total_similarity = (name_similarity * 0.4 + desc_similarity * 0.3 +
                          type_similarity * 0.2 + prop_similarity * 0.1)

        return total_similarity

    def _get_dynamic_similarity_threshold(self, group1: List[Entity], group2: List[Entity]) -> float:
        """根据实体信息丰富程度动态调整相似度阈值"""
        # 基础阈值
        base_threshold = 0.75

        # 计算信息丰富度
        richness1 = self._calculate_entity_group_richness(group1)
        richness2 = self._calculate_entity_group_richness(group2)
        avg_richness = (richness1 + richness2) / 2

        # 信息越丰富，阈值越高（更严格）
        if avg_richness > 0.8:
            return 0.85
        elif avg_richness > 0.6:
            return 0.80
        elif avg_richness > 0.4:
            return 0.75
        else:
            return 0.70  # 信息较少时，阈值较低（更宽松）

    def _calculate_name_similarity(self, name1: str, name2: str) -> float:
        """计算名称相似度"""
        # 标准化名称
        norm1 = self._normalize_entity_name_enhanced(name1)
        norm2 = self._normalize_entity_name_enhanced(name2)

        if norm1 == norm2:
            return 1.0

        # 使用编辑距离计算相似度
        from difflib import SequenceMatcher
        return SequenceMatcher(None, norm1, norm2).ratio()

    def _calculate_description_similarity(self, desc1: str, desc2: str) -> float:
        """计算描述相似度"""
        if not desc1 or not desc2:
            return 0.0

        # 简单的词汇重叠度计算
        words1 = set(desc1.lower().split())
        words2 = set(desc2.lower().split())

        if not words1 or not words2:
            return 0.0

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union) if union else 0.0

    def _calculate_properties_similarity(self, props1: dict, props2: dict) -> float:
        """计算属性相似度"""
        if not props1 or not props2:
            return 0.0

        # 计算共同属性的比例
        keys1 = set(props1.keys())
        keys2 = set(props2.keys())

        if not keys1 or not keys2:
            return 0.0

        common_keys = keys1.intersection(keys2)
        all_keys = keys1.union(keys2)

        return len(common_keys) / len(all_keys) if all_keys else 0.0

    def _calculate_entity_group_richness(self, group: List[Entity]) -> float:
        """计算实体组的信息丰富度"""
        if not group:
            return 0.0

        total_richness = 0.0
        for entity in group:
            richness = 0.0

            # 名称长度贡献
            if entity.name:
                richness += min(len(entity.name) / 20.0, 0.3)

            # 描述贡献
            if entity.description:
                richness += min(len(entity.description) / 100.0, 0.4)

            # 类型贡献
            if entity.type:
                richness += 0.2

            # 属性贡献
            if entity.properties:
                richness += min(len(entity.properties) / 10.0, 0.1)

            total_richness += min(richness, 1.0)

        return total_richness / len(group)

    def _merge_entity_group_enhanced(self, entities: List[Entity]) -> Entity:
        """增强的实体组合并，智能合并属性和关系信息"""
        from loguru import logger

        if len(entities) == 1:
            return entities[0]

        logger.debug(f"合并实体组: {[e.name for e in entities]}")

        # 1. 选择最完整的实体作为主实体
        primary_entity = self._select_primary_entity(entities)

        # 2. 合并所有实体的信息
        merged_name = primary_entity.name
        merged_type = primary_entity.type
        merged_description = self._merge_descriptions(entities)
        merged_properties = self._merge_properties(entities)

        # 3. 记录合并信息
        merged_properties["merged_from"] = [e.id for e in entities if e.id != primary_entity.id]
        merged_properties["variant_names"] = [e.name for e in entities if e.name != primary_entity.name]
        merged_properties["merge_count"] = len(entities)

        # 4. 创建合并后的实体
        merged_entity = Entity(
            id=primary_entity.id,
            name=merged_name,
            type=merged_type,
            description=merged_description,
            properties=merged_properties
        )

        logger.debug(f"实体合并完成: {merged_name} (合并了 {len(entities)} 个实体)")
        return merged_entity

    def _select_primary_entity(self, entities: List[Entity]) -> Entity:
        """选择最适合作为主实体的实体"""
        # 评分标准：描述长度 + 属性数量 + 名称长度
        def entity_score(entity):
            desc_score = len(entity.description or "") * 2
            prop_score = len(entity.properties) * 10
            name_score = len(entity.name)
            return desc_score + prop_score + name_score

        return max(entities, key=entity_score)

    def _merge_descriptions(self, entities: List[Entity]) -> str:
        """智能合并实体描述"""
        descriptions = []
        seen_descriptions = set()

        for entity in entities:
            if entity.description and entity.description.strip():
                desc = entity.description.strip()
                # 避免重复描述
                if desc not in seen_descriptions:
                    descriptions.append(desc)
                    seen_descriptions.add(desc)

        if not descriptions:
            return ""

        # 如果只有一个描述，直接返回
        if len(descriptions) == 1:
            return descriptions[0]

        # 多个描述时，用分号连接
        return "; ".join(descriptions)

    def _merge_properties(self, entities: List[Entity]) -> Dict[str, Any]:
        """智能合并实体属性"""
        merged_props = {}

        for entity in entities:
            for key, value in entity.properties.items():
                if key not in merged_props:
                    merged_props[key] = value
                else:
                    # 处理属性值冲突
                    existing_value = merged_props[key]
                    if existing_value != value:
                        # 如果值不同，创建列表保存所有值
                        if isinstance(existing_value, list):
                            if value not in existing_value:
                                existing_value.append(value)
                        else:
                            merged_props[key] = [existing_value, value]

        return merged_props

    async def _enhance_relations_after_merge(self, entities: List[Entity], relations: List[Relation]) -> List[Relation]:
        """在实体合并后增强关系"""
        from loguru import logger

        logger.info("开始增强合并后的关系...")
        enhanced_relations = relations.copy()

        # 1. 从合并的实体信息中发现新关系
        merge_based_relations = self._discover_relations_from_merged_entities(entities)
        enhanced_relations.extend(merge_based_relations)
        logger.info(f"从合并信息发现 {len(merge_based_relations)} 个新关系")

        # 2. 基于实体描述推理关系
        if settings.ENABLE_RELATION_INFERENCE:
            description_relations = await self._infer_relations_from_descriptions_enhanced(entities)
            enhanced_relations.extend(description_relations)
            logger.info(f"从描述推理出 {len(description_relations)} 个新关系")

        # 3. 基于实体属性发现关系
        property_relations = self._discover_relations_from_properties(entities)
        enhanced_relations.extend(property_relations)
        logger.info(f"从属性发现 {len(property_relations)} 个新关系")

        # 4. 去重关系
        unique_relations = self._deduplicate_relations(enhanced_relations)
        logger.info(f"关系去重: {len(enhanced_relations)} -> {len(unique_relations)}")

        return unique_relations

    def _discover_relations_from_merged_entities(self, entities: List[Entity]) -> List[Relation]:
        """从合并的实体信息中发现新关系"""
        new_relations = []

        for entity in entities:
            # 检查是否有合并信息
            if "merged_from" not in entity.properties:
                continue

            merged_ids = entity.properties.get("merged_from", [])
            variant_names = entity.properties.get("variant_names", [])

            # 为每个变体名称创建"别名"关系
            for variant_name in variant_names:
                relation = Relation(
                    id=str(uuid.uuid4()),
                    source_entity=entity.id,
                    target_entity=entity.id,  # 自引用
                    relation_type="别名",
                    properties={
                        "alias": variant_name,
                        "confidence": 1.0,
                        "source": "entity_merge"
                    }
                )
                new_relations.append(relation)

        return new_relations

    async def _infer_relations_from_descriptions_enhanced(self, entities: List[Entity]) -> List[Relation]:
        """增强的从描述推理关系"""
        if not settings.ENABLE_RELATION_INFERENCE:
            return []

        from loguru import logger
        new_relations = []

        # 为每个有描述的实体分析其与其他实体的关系
        for entity in entities:
            if not entity.description:
                continue

            # 查找描述中提到的其他实体
            mentioned_entities = []
            description_lower = entity.description.lower()

            for other_entity in entities:
                if other_entity.id == entity.id:
                    continue

                # 检查主名称
                if other_entity.name.lower() in description_lower:
                    mentioned_entities.append(other_entity)
                    continue

                # 检查变体名称
                variant_names = other_entity.properties.get("variant_names", [])
                for variant in variant_names:
                    if variant.lower() in description_lower:
                        mentioned_entities.append(other_entity)
                        break

            # 简化版本：直接创建"提及"关系，不使用LLM推理
            if mentioned_entities:
                logger.debug(f"实体 {entity.name} 的描述中提到了 {len(mentioned_entities)} 个其他实体")
                for mentioned_entity in mentioned_entities:
                    relation = Relation(
                        id=str(uuid.uuid4()),
                        source_entity=entity.id,
                        target_entity=mentioned_entity.id,
                        relation_type="提及",
                        properties={
                            "confidence": 0.8,
                            "source": "description_inference",
                            "inference_method": "text_mention"
                        }
                    )
                    new_relations.append(relation)

        return new_relations

    def _discover_relations_from_properties(self, entities: List[Entity]) -> List[Relation]:
        """从实体属性中发现关系"""
        new_relations = []

        # 创建实体映射
        entity_map = {entity.id: entity for entity in entities}
        name_to_entity = {entity.name.lower(): entity for entity in entities}

        for entity in entities:
            # 分析属性中可能包含的关系信息
            for prop_key, prop_value in entity.properties.items():
                if isinstance(prop_value, str):
                    # 查找属性值中提到的其他实体
                    prop_value_lower = prop_value.lower()

                    for other_entity in entities:
                        if other_entity.id == entity.id:
                            continue

                        if other_entity.name.lower() in prop_value_lower:
                            # 根据属性键推断关系类型
                            relation_type = self._infer_relation_type_from_property(prop_key)

                            relation = Relation(
                                id=str(uuid.uuid4()),
                                source_entity=entity.id,
                                target_entity=other_entity.id,
                                relation_type=relation_type,
                                properties={
                                    "confidence": 0.7,
                                    "source": "property_analysis",
                                    "property_key": prop_key,
                                    "property_value": prop_value
                                }
                            )
                            new_relations.append(relation)

        return new_relations

    def _infer_relation_type_from_property(self, property_key: str) -> str:
        """根据属性键推断关系类型"""
        property_key_lower = property_key.lower()

        # 定义属性键到关系类型的映射
        property_relation_map = {
            "师父": "师徒关系",
            "弟子": "师徒关系",
            "朋友": "朋友关系",
            "敌人": "敌对关系",
            "组织": "隶属关系",
            "门派": "隶属关系",
            "宗门": "隶属关系",
            "家族": "家族关系",
            "父亲": "家族关系",
            "母亲": "家族关系",
            "儿子": "家族关系",
            "女儿": "家族关系",
            "配偶": "婚姻关系",
            "妻子": "婚姻关系",
            "丈夫": "婚姻关系",
            "位置": "位于",
            "地点": "位于",
            "居住": "居住于"
        }

        for key_pattern, relation_type in property_relation_map.items():
            if key_pattern in property_key_lower:
                return relation_type

        # 默认关系类型
        return "相关"

    async def _fix_isolated_nodes(self, kg: KnowledgeGraph, isolated_node_ids: List[str]) -> List[Relation]:
        """修复孤立节点，为它们寻找可能的关系"""
        additional_relations = []

        # 获取孤立节点的实体
        isolated_entities = [e for e in kg.entities if e.id in isolated_node_ids]
        connected_entities = [e for e in kg.entities if e.id not in isolated_node_ids]

        if not connected_entities:
            logger.warning("没有连通的实体可以与孤立节点建立关系")
            return additional_relations

        logger.info(f"尝试为 {len(isolated_entities)} 个孤立节点寻找关系")

        for isolated_entity in isolated_entities:
            # 方法1: 基于实体描述寻找关系
            if isolated_entity.description:
                relations = await self._find_relations_by_description(isolated_entity, connected_entities)
                additional_relations.extend(relations)

            # 方法2: 基于实体类型寻找关系
            type_relations = self._find_relations_by_type(isolated_entity, connected_entities)
            additional_relations.extend(type_relations)

            # 方法3: 基于名称相似度寻找关系
            similarity_relations = self._find_relations_by_similarity(isolated_entity, connected_entities)
            additional_relations.extend(similarity_relations)

        # 去重
        unique_relations = self._deduplicate_relations(additional_relations)

        logger.info(f"为孤立节点找到 {len(unique_relations)} 个潜在关系")
        return unique_relations

    async def _find_relations_by_description(self, isolated_entity: Entity, connected_entities: List[Entity]) -> List[Relation]:
        """基于实体描述寻找关系"""
        relations = []

        # 查找描述中提到的其他实体
        mentioned_entities = []
        for entity in connected_entities:
            if entity.name.lower() in isolated_entity.description.lower():
                mentioned_entities.append(entity)

        if mentioned_entities:
            # 使用LLM推理关系
            inferred_relations = await self.disambiguation_service._infer_relations_with_llm(
                isolated_entity, mentioned_entities
            )
            relations.extend(inferred_relations)

        return relations

    def _find_relations_by_type(self, isolated_entity: Entity, connected_entities: List[Entity]) -> List[Relation]:
        """基于实体类型寻找关系"""
        relations = []

        # 定义类型间的常见关系
        type_relations = {
            ("person", "organization"): "works_for",
            ("person", "location"): "located_in",
            ("organization", "location"): "based_in",
            ("product", "organization"): "developed_by",
            ("project", "person"): "managed_by",
            ("technology", "person"): "used_by",
        }

        for entity in connected_entities:
            # 查找类型匹配的关系
            type_pair = (isolated_entity.type.lower(), entity.type.lower())
            reverse_type_pair = (entity.type.lower(), isolated_entity.type.lower())

            if type_pair in type_relations:
                import uuid
                relation = Relation(
                    id=str(uuid.uuid4()),
                    source_entity=isolated_entity.id,
                    target_entity=entity.id,
                    relation_type=type_relations[type_pair],
                    confidence=0.6,
                    properties={
                        "inferred_from": "entity_type",
                        "description": f"基于实体类型推断的关系"
                    }
                )
                relations.append(relation)
            elif reverse_type_pair in type_relations:
                import uuid
                relation = Relation(
                    id=str(uuid.uuid4()),
                    source_entity=entity.id,
                    target_entity=isolated_entity.id,
                    relation_type=type_relations[reverse_type_pair],
                    confidence=0.6,
                    properties={
                        "inferred_from": "entity_type",
                        "description": f"基于实体类型推断的关系"
                    }
                )
                relations.append(relation)

        return relations

    def _find_relations_by_similarity(self, isolated_entity: Entity, connected_entities: List[Entity]) -> List[Relation]:
        """基于名称相似度寻找关系"""
        relations = []

        for entity in connected_entities:
            # 计算名称相似度
            similarity = self.disambiguation_service.calculate_entity_similarity(isolated_entity, entity)

            # 如果相似度较高但不足以合并，建立弱关系
            if 0.5 <= similarity < settings.ENTITY_SIMILARITY_THRESHOLD:
                import uuid
                relation = Relation(
                    id=str(uuid.uuid4()),
                    source_entity=isolated_entity.id,
                    target_entity=entity.id,
                    relation_type="related",
                    confidence=similarity,
                    properties={
                        "inferred_from": "name_similarity",
                        "similarity_score": similarity,
                        "description": f"基于名称相似度推断的关系"
                    }
                )
                relations.append(relation)

        return relations

    def _deduplicate_relations(self, relations: List[Relation]) -> List[Relation]:
        """去除重复关系"""
        seen = set()
        unique_relations = []

        for relation in relations:
            # 创建关系的唯一标识
            key = (relation.source_entity, relation.target_entity, relation.relation_type)
            reverse_key = (relation.target_entity, relation.source_entity, relation.relation_type)

            if key not in seen and reverse_key not in seen:
                seen.add(key)
                unique_relations.append(relation)

        return unique_relations

    async def _enhanced_entity_disambiguation(
        self,
        entities: List[Entity],
        relations: List[Relation]
    ) -> Tuple[List[Entity], Dict[str, str], List[Dict]]:
        """增强的实体消歧处理"""
        if not settings.ENABLE_ENTITY_DISAMBIGUATION:
            # 如果禁用消歧，使用原有逻辑
            merged_entities = self._merge_duplicate_entities(entities)
            return merged_entities, {}, []

        logger.info(f"开始实体消歧处理，原始实体数: {len(entities)}")

        # 1. 找到实体变体（根据配置选择识别方法）
        if settings.ENABLE_LLM_ENTITY_DISAMBIGUATION:
            try:
                entity_variants = await self.disambiguation_service.find_entity_variants_with_llm(entities)
                logger.info(f"LLM识别发现 {len(entity_variants)} 组实体变体")
            except Exception as e:
                logger.error(f"LLM实体变体识别失败，回退到基础方法: {e}")
                entity_variants = self.disambiguation_service.find_entity_variants(entities)
                logger.info(f"基础方法发现 {len(entity_variants)} 组实体变体")
        else:
            entity_variants = self.disambiguation_service.find_entity_variants(entities)
            logger.info(f"基础方法发现 {len(entity_variants)} 组实体变体")

        # 2. 合并实体变体
        merged_entities_dict, id_mapping = self.disambiguation_service.merge_entity_variants(entity_variants)

        # 3. 保留未合并的实体
        merged_entity_ids = set(merged_entities_dict.keys())
        for entity in entities:
            if entity.id not in id_mapping and entity.id not in merged_entity_ids:
                merged_entities_dict[entity.id] = entity
                id_mapping[entity.id] = entity.id

        merged_entities = list(merged_entities_dict.values())

        # 4. 收集未匹配的关系
        unmatched_relations = []
        entity_name_map = {entity.name.lower().strip(): entity.id for entity in merged_entities}

        for relation in relations:
            # 检查关系的实体是否存在
            source_exists = any(e.id == relation.source_entity for e in merged_entities)
            target_exists = any(e.id == relation.target_entity for e in merged_entities)

            if not source_exists or not target_exists:
                # 尝试通过名称匹配
                source_name = None
                target_name = None

                # 从原始实体中查找名称
                for entity in entities:
                    if entity.id == relation.source_entity:
                        source_name = entity.name
                    if entity.id == relation.target_entity:
                        target_name = entity.name

                if source_name and target_name:
                    unmatched_relations.append({
                        "source": source_name,
                        "target": target_name,
                        "relation": relation.relation_type,
                        "description": relation.properties.get("description", "")
                    })

        logger.info(f"实体消歧完成: {len(entities)} -> {len(merged_entities)} 个实体, {len(unmatched_relations)} 个未匹配关系")

        return merged_entities, id_mapping, unmatched_relations

    def _update_relation_entity_ids_with_mapping(
        self,
        relations: List[Relation],
        entities: List[Entity],
        id_mapping: Dict[str, str]
    ) -> List[Relation]:
        """使用ID映射更新关系中的实体ID"""
        updated_relations = []
        entity_ids = {entity.id for entity in entities}

        for relation in relations:
            # 使用映射更新实体ID
            source_id = id_mapping.get(relation.source_entity, relation.source_entity)
            target_id = id_mapping.get(relation.target_entity, relation.target_entity)

            # 检查实体是否存在
            if source_id in entity_ids and target_id in entity_ids:
                updated_relation = Relation(
                    id=relation.id,
                    source_entity=source_id,
                    target_entity=target_id,
                    relation_type=relation.relation_type,
                    confidence=relation.confidence,
                    source_text=relation.source_text,
                    properties=relation.properties
                )
                updated_relations.append(updated_relation)

        return updated_relations

    def _update_relation_entity_ids(
        self, 
        relations: List[Relation], 
        entities: List[Entity]
    ) -> List[Relation]:
        """更新关系中的实体ID"""
        # 创建实体名称到ID的映射
        name_to_id = {entity.name.lower().strip(): entity.id for entity in entities}
        
        updated_relations = []
        for relation in relations:
            # 查找对应的实体ID
            source_entity = None
            target_entity = None
            
            for entity in entities:
                if entity.id == relation.source_entity:
                    source_entity = entity.id
                elif entity.id == relation.target_entity:
                    target_entity = entity.id
            
            if source_entity and target_entity:
                updated_relations.append(relation)
        
        return updated_relations
    
    async def save_knowledge_graph(self, kg: KnowledgeGraph) -> None:
        """保存知识图谱到文件"""
        file_path = self.kg_data_dir / f"{kg.id}.json"

        try:
            # 转换为可序列化的字典
            kg_dict = {
                "id": kg.id,
                "name": kg.name,
                "entities": [entity.model_dump() for entity in kg.entities],
                "relations": [relation.model_dump() for relation in kg.relations],
                "metadata": kg.metadata,
                "created_at": kg.created_at.isoformat(),
                "updated_at": kg.updated_at.isoformat()
            }

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(kg_dict, f, ensure_ascii=False, indent=2, default=str)

            logger.info(f"知识图谱已保存: {file_path}")
        except Exception as e:
            logger.error(f"保存知识图谱失败: {e}")
            raise
    
    async def load_knowledge_graph(self, kg_id: str) -> Optional[KnowledgeGraph]:
        """从文件加载知识图谱"""
        file_path = self.kg_data_dir / f"{kg_id}.json"
        
        if not file_path.exists():
            return None
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                kg_dict = json.load(f)
            
            # 重建实体和关系对象
            entities = [Entity(**entity_data) for entity_data in kg_dict["entities"]]
            relations = [Relation(**relation_data) for relation_data in kg_dict["relations"]]
            
            kg = KnowledgeGraph(
                id=kg_dict["id"],
                name=kg_dict["name"],
                entities=entities,
                relations=relations,
                metadata=kg_dict["metadata"],
                created_at=datetime.fromisoformat(kg_dict["created_at"]),
                updated_at=datetime.fromisoformat(kg_dict["updated_at"])
            )
            
            self.graphs[kg_id] = kg
            return kg
            
        except Exception as e:
            logger.error(f"加载知识图谱失败 {kg_id}: {e}")
            return None
    
    def search_entities(self, kg_id: str, query: str, limit: int = 10) -> GraphSearchResult:
        """搜索实体"""
        if kg_id not in self.graphs:
            return GraphSearchResult()
        
        kg = self.graphs[kg_id]
        query_lower = query.lower()
        
        # 搜索匹配的实体
        matched_entities = []
        for entity in kg.entities:
            if (query_lower in entity.name.lower() or 
                (entity.description and query_lower in entity.description.lower())):
                matched_entities.append(entity)
        
        # 限制结果数量
        matched_entities = matched_entities[:limit]
        
        # 查找相关关系
        entity_ids = {entity.id for entity in matched_entities}
        related_relations = []
        
        for relation in kg.relations:
            if (relation.source_entity in entity_ids or 
                relation.target_entity in entity_ids):
                related_relations.append(relation)
        
        return GraphSearchResult(
            entities=matched_entities,
            relations=related_relations
        )
    
    def get_entity_neighbors(self, kg_id: str, entity_id: str, depth: int = 1) -> GraphSearchResult:
        """获取实体的邻居节点"""
        if kg_id not in self.graphs:
            return GraphSearchResult()
        
        kg = self.graphs[kg_id]
        
        # 转换为NetworkX图
        G = kg.to_networkx()
        
        if entity_id not in G:
            return GraphSearchResult()
        
        # 获取指定深度内的邻居
        neighbors = set([entity_id])
        for _ in range(depth):
            new_neighbors = set()
            for node in neighbors:
                new_neighbors.update(G.neighbors(node))
            neighbors.update(new_neighbors)
        
        # 构建子图
        subgraph = G.subgraph(neighbors)
        
        # 提取实体和关系
        result_entities = [entity for entity in kg.entities if entity.id in neighbors]
        result_relations = [
            relation for relation in kg.relations 
            if relation.source_entity in neighbors and relation.target_entity in neighbors
        ]
        
        return GraphSearchResult(
            entities=result_entities,
            relations=result_relations,
            subgraph=self._networkx_to_dict(subgraph)
        )
    
    def get_graph_stats(self, kg_id: str) -> Optional[GraphStats]:
        """获取图统计信息"""
        if kg_id not in self.graphs:
            return None
        
        kg = self.graphs[kg_id]
        G = kg.to_networkx()
        
        # 统计实体类型
        entity_types = {}
        for entity in kg.entities:
            entity_types[entity.type] = entity_types.get(entity.type, 0) + 1
        
        # 统计关系类型
        relation_types = {}
        for relation in kg.relations:
            relation_types[relation.relation_type] = relation_types.get(relation.relation_type, 0) + 1
        
        # 计算图统计指标
        avg_degree = sum(dict(G.degree()).values()) / len(G.nodes()) if G.nodes() else 0
        density = nx.density(G)
        
        return GraphStats(
            total_entities=len(kg.entities),
            total_relations=len(kg.relations),
            entity_types=entity_types,
            relation_types=relation_types,
            avg_degree=avg_degree,
            density=density
        )
    
    def _networkx_to_dict(self, graph: nx.Graph) -> Dict[str, Any]:
        """将NetworkX图转换为字典格式"""
        nodes = []
        edges = []
        
        for node_id, node_data in graph.nodes(data=True):
            nodes.append({
                "id": node_id,
                "name": node_data.get("name", node_id),
                "type": node_data.get("type", "unknown"),
                **node_data
            })
        
        for source, target, edge_data in graph.edges(data=True):
            edges.append({
                "source": source,
                "target": target,
                "relation_type": edge_data.get("relation_type", "related"),
                **edge_data
            })
        
        return {
            "nodes": nodes,
            "edges": edges
        }
    
    def list_knowledge_graphs(self) -> List[Dict[str, Any]]:
        """列出所有知识图谱"""
        graphs_info = []
        
        for file_path in self.kg_data_dir.glob("*.json"):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    kg_dict = json.load(f)
                
                graphs_info.append({
                    "id": kg_dict["id"],
                    "name": kg_dict["name"],
                    "entity_count": len(kg_dict["entities"]),
                    "relation_count": len(kg_dict["relations"]),
                    "created_at": kg_dict["created_at"],
                    "updated_at": kg_dict["updated_at"]
                })
            except Exception as e:
                logger.error(f"读取图谱文件失败 {file_path}: {e}")
                continue
        
        return graphs_info

    async def _cleanup_after_success(self, kg_id: str, file_paths: List[str]) -> None:
        """成功生成知识图谱后的清理工作"""
        try:
            # 删除指定的文件
            for file_path in file_paths:
                await self.file_service.delete_file(file_path)

            # 可选：清理所有上传文件（如果需要的话）
            # await self.file_service.cleanup_uploads_directory()

            logger.info(f"知识图谱 {kg_id} 生成成功，已清理相关文件")
        except Exception as e:
            logger.error(f"清理文件时出错: {e}")

    async def cleanup_uploads_after_kg_generation(self, kg_id: str) -> int:
        """在知识图谱生成后清理uploads目录"""
        try:
            deleted_count = await self.file_service.cleanup_uploads_directory()
            logger.info(f"知识图谱 {kg_id} 生成完成，清理了 {deleted_count} 个上传文件")
            return deleted_count
        except Exception as e:
            logger.error(f"清理uploads目录失败: {e}")
            return 0
