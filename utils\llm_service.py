"""
LLM 服务工具模块
提供大语言模型API调用、实体关系提取、文本预处理等功能
"""
import asyncio
import httpx
import json
import time
from typing import List, Dict, Any, Tuple, Optional, Callable

from app.config import settings
from app.models import Entity, Relation


class LLMService:
    """大语言模型服务类"""

    def __init__(self):
        self.api_base = settings.LLM_API_BASE
        self.api_key = settings.LLM_API_KEY
        self.model = settings.LLM_MODEL
        self.max_tokens = settings.LLM_MAX_TOKENS
        self.temperature = settings.LLM_TEMPERATURE

        # 并发控制
        self.max_concurrent = settings.MAX_CONCURRENT_CHUNKS
        self.semaphore = asyncio.Semaphore(self.max_concurrent)

        # HTTP客户端配置
        self.client_config = {
            "timeout": httpx.Timeout(settings.LLM_REQUEST_TIMEOUT),
            "limits": httpx.Limits(
                max_connections=settings.CONNECTION_POOL_SIZE,
                max_keepalive_connections=settings.CONNECTION_POOL_SIZE // 2
            )
        }
        self._client = None

    async def get_client(self) -> httpx.AsyncClient:
        """获取HTTP客户端实例（单例模式）"""
        if self._client is None or self._client.is_closed:
            self._client = httpx.AsyncClient(**self.client_config)
        return self._client

    async def close_client(self):
        """关闭HTTP客户端"""
        if self._client and not self._client.is_closed:
            await self._client.aclose()
            self._client = None

    async def _call_llm(self, messages: List[Dict[str, str]]) -> str:
        """调用LLM API（带重试机制）"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "model": self.model,
            "messages": messages,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature
        }

        # 使用信号量控制并发
        async with self.semaphore:
            client = await self.get_client()

            for attempt in range(settings.LLM_MAX_RETRIES):
                try:
                    response = await client.post(
                        f"{self.api_base}/chat/completions",
                        headers=headers,
                        json=payload
                    )
                    response.raise_for_status()
                    result = response.json()
                    return result["choices"][0]["message"]["content"]

                except Exception as e:
                    if attempt < settings.LLM_MAX_RETRIES - 1:
                        await asyncio.sleep(settings.LLM_RETRY_DELAY * (attempt + 1))
                    else:
                        raise Exception(f"LLM API调用失败: {e}")

    async def extract_entities_and_relations(self, text: str) -> Tuple[List[Entity], List[Relation]]:
        """从文本中提取实体和关系"""
        if settings.RELATION_EXTRACTION_FOCUS:
            return await self._enhanced_extract_entities_and_relations(text)
        else:
            return await self._basic_extract_entities_and_relations(text)

    async def _basic_extract_entities_and_relations(self, text: str) -> Tuple[List[Entity], List[Relation]]:
        """基础实体关系提取"""
        prompt = f"""
请从以下文本中提取实体和关系，并严格按照JSON格式返回。

要求：
1. 提取重要的实体（人物、地点、组织、概念、事件等）
2. 识别实体之间的关系
3. 必须返回有效的JSON格式，不要包含任何其他文字
4. 如果没有找到实体或关系，返回空数组

返回格式（只返回JSON，不要其他内容）：
{{
    "entities": [
        {{
            "name": "实体名称",
            "type": "实体类型",
            "description": "实体描述"
        }}
    ],
    "relations": [
        {{
            "source": "源实体名称",
            "target": "目标实体名称",
            "relation": "关系类型",
            "description": "关系描述"
        }}
    ]
}}

文本内容：
{text[:1500]}
"""
        return await self._process_extraction_prompt(prompt, text)

    async def _enhanced_extract_entities_and_relations(self, text: str) -> Tuple[List[Entity], List[Relation]]:
        """增强实体关系提取"""
        prompt = f"""
请深度分析以下文本，提取所有重要的实体和关系。特别关注关系的识别和分类。

## 实体提取要求：
1. **人物**：姓名、职位、角色等
2. **组织**：公司、机构、团队等
3. **地点**：城市、地区、具体地址等
4. **概念**：技术、理论、方法等
5. **事件**：项目、活动、事件等
6. **产品**：软件、硬件、服务等
7. **时间**：日期、时期、阶段等

## 关系提取要求：
请识别以下类型的关系，并尽可能多地提取：
1. **工作关系**：雇佣、管理、合作、负责等
2. **地理关系**：位于、来自、在...工作等
3. **从属关系**：属于、包含、隶属等
4. **技能关系**：擅长、使用、开发等
5. **项目关系**：参与、负责、开发等
6. **时间关系**：发生在、持续、开始等
7. **因果关系**：导致、影响、促进等
8. **社交关系**：认识、朋友、同事等

## 输出要求：
- 必须返回有效的JSON格式
- 关系数量应该尽可能多，不要遗漏
- 每个关系都要有清晰的描述
- 实体描述要详细具体

返回格式（只返回JSON）：
{{
    "entities": [
        {{
            "name": "实体名称",
            "type": "实体类型",
            "description": "详细描述，包括背景信息"
        }}
    ],
    "relations": [
        {{
            "source": "源实体名称",
            "target": "目标实体名称",
            "relation": "具体关系类型",
            "description": "关系的详细描述和上下文"
        }}
    ]
}}

文本内容：
{text[:3000]}
"""
        return await self._process_extraction_prompt(prompt, text)

    async def _process_extraction_prompt(self, prompt: str, text: str) -> Tuple[List[Entity], List[Relation]]:
        """处理实体关系提取提示词"""
        messages = [
            {"role": "system", "content": "你是一个专业的知识图谱构建助手，擅长从文本中提取实体和关系。请严格按照JSON格式返回结果，不要包含任何解释文字。特别注意要尽可能多地提取关系，不要遗漏任何有价值的实体间连接。"},
            {"role": "user", "content": prompt}
        ]

        try:
            response = await self._call_llm(messages)
            
            # 清理响应，移除可能的markdown标记
            response = response.strip()
            if response.startswith("```json"):
                response = response[7:]
            if response.endswith("```"):
                response = response[:-3]
            response = response.strip()

            # 解析JSON
            data = json.loads(response)
            
            # 创建实体对象
            entities = []
            for entity_data in data.get("entities", []):
                entity = Entity(
                    name=entity_data.get("name", ""),
                    type=entity_data.get("type", "unknown"),
                    description=entity_data.get("description", "")
                )
                entities.append(entity)
            
            # 创建关系对象
            relations = []
            for relation_data in data.get("relations", []):
                relation = Relation(
                    source=relation_data.get("source", ""),
                    target=relation_data.get("target", ""),
                    relation=relation_data.get("relation", ""),
                    description=relation_data.get("description", "")
                )
                relations.append(relation)
            
            return entities, relations

        except json.JSONDecodeError as e:
            # JSON解析失败，返回空结果
            return [], []
        except Exception as e:
            # 其他错误，返回空结果
            return [], []

    async def extract_entities_and_relations_batch(
        self,
        texts: List[str],
        progress_callback: Optional[Callable[[int, int], None]] = None
    ) -> List[Tuple[List[Entity], List[Relation]]]:
        """批量并发提取实体和关系"""
        if not settings.ENABLE_PARALLEL_PROCESSING:
            # 如果禁用并行处理，回退到串行处理
            results = []
            for i, text in enumerate(texts):
                result = await self.extract_entities_and_relations(text)
                results.append(result)
                if progress_callback:
                    progress_callback(i + 1, len(texts))
            return results

        # 并发处理
        async def process_with_progress(i: int, text: str):
            """带进度回调的处理函数"""
            try:
                result = await self.extract_entities_and_relations(text)
                if progress_callback:
                    progress_callback(i + 1, len(texts))
                return result
            except Exception as e:
                if progress_callback:
                    progress_callback(i + 1, len(texts))
                return [], []  # 返回空结果而不是抛出异常

        # 创建任务列表
        tasks = [
            process_with_progress(i, text)
            for i, text in enumerate(texts)
        ]

        # 并发执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append(([], []))  # 空结果
            else:
                processed_results.append(result)

        return processed_results

    async def summarize_text(self, text: str) -> str:
        """文本摘要"""
        prompt = f"""
请对以下文本进行摘要，提取主要内容和关键信息：

{text[:3000]}
"""
        
        messages = [
            {"role": "system", "content": "你是一个专业的文本摘要助手。"},
            {"role": "user", "content": prompt}
        ]
        
        try:
            return await self._call_llm(messages)
        except Exception as e:
            return "摘要生成失败"

    async def classify_entity_type(self, entity_name: str, context: str = "") -> str:
        """实体类型分类"""
        prompt = f"""
请判断实体"{entity_name}"的类型。

上下文：{context[:500]}

可能的类型包括：人物、地点、组织、概念、事件、产品、时间等。

请只返回类型名称。
"""

        messages = [
            {"role": "system", "content": "你是一个实体分类专家。"},
            {"role": "user", "content": prompt}
        ]

        try:
            result = await self._call_llm(messages)
            return result.strip()
        except Exception as e:
            return "unknown"

    async def preprocess_text_for_kg(self, text: str, filename: str = "") -> str:
        """为知识图谱构建预处理文本"""
        if not settings.ENABLE_ENHANCED_PREPROCESSING:
            return await self._simple_preprocess_text(text, filename)

        # 检查文本长度，决定是否需要分段处理
        if len(text) <= settings.PREPROCESS_SEGMENT_SIZE:
            return await self._enhanced_preprocess_segment(text, filename, is_single=True)

        # 长文本，分段处理
        return await self._preprocess_long_text(text, filename)

    async def _simple_preprocess_text(self, text: str, filename: str = "") -> str:
        """简单预处理"""
        prompt = f"""
请对以下文本进行预处理，为知识图谱构建做准备。

要求：
1. 整理和结构化文本内容
2. 识别并标注重要的实体（人物、地点、组织、概念等）
3. 明确实体之间的关系
4. 保持原文的核心信息和语义
5. 输出结构化的文本，便于后续的实体关系抽取

文件名：{filename}

原始文本：
{text[:8000]}
"""

        messages = [
            {"role": "system", "content": "你是一个专业的文本预处理专家，擅长为知识图谱构建准备结构化文本。"},
            {"role": "user", "content": prompt}
        ]

        try:
            result = await self._call_llm(messages)
            return result.strip()
        except Exception as e:
            return text  # 如果预处理失败，返回原文

    async def _preprocess_long_text(self, text: str, filename: str = "") -> str:
        """长文本分段预处理"""
        segments = self._split_text_into_segments(text)
        processed_segments = []

        # 限制处理的段数
        segments_to_process = segments[:settings.MAX_PREPROCESS_SEGMENTS]

        for i, segment in enumerate(segments_to_process):
            try:
                processed_segment = await self._enhanced_preprocess_segment(
                    segment,
                    filename,
                    segment_index=i+1,
                    total_segments=len(segments_to_process)
                )
                processed_segments.append(processed_segment)
            except Exception as e:
                # 如果预处理失败，使用原始段落
                processed_segments.append(f"=== 段落 {i+1} ===\n{segment}")

        # 合并所有处理后的段落
        return self._merge_processed_segments(processed_segments, filename)

    def _split_text_into_segments(self, text: str) -> List[str]:
        """将长文本分割为重叠的段落"""
        segments = []
        segment_size = settings.PREPROCESS_SEGMENT_SIZE
        overlap_size = settings.PREPROCESS_OVERLAP_SIZE

        start = 0
        while start < len(text):
            end = start + segment_size

            # 如果不是最后一段，尝试在句号或段落处分割
            if end < len(text):
                # 向后查找合适的分割点
                for i in range(min(200, len(text) - end)):
                    if text[end + i] in ['。', '\n\n', '！', '？']:
                        end = end + i + 1
                        break

            segment = text[start:end].strip()
            if segment:
                segments.append(segment)

            # 计算下一段的起始位置（考虑重叠）
            start = end - overlap_size
            if start >= len(text):
                break

        return segments

    async def _enhanced_preprocess_segment(
        self,
        segment: str,
        filename: str = "",
        is_single: bool = False,
        segment_index: int = 1,
        total_segments: int = 1
    ) -> str:
        """增强预处理单个段落"""
        segment_info = f"段落 {segment_index}/{total_segments}" if not is_single else "完整文档"

        prompt = f"""
请对以下文本段落进行深度预处理，为知识图谱构建做准备。

文档信息：
- 文件名：{filename}
- 处理范围：{segment_info}

预处理要求：
1. **实体识别与标注**：
   - 识别所有重要实体（人物、地点、组织、概念、事件、产品、时间等）
   - 为每个实体添加类型标注：[实体名称|类型]
   - 保留实体的完整描述信息

2. **关系识别与标注**：
   - 识别实体间的各种关系（工作关系、地理关系、从属关系、因果关系等）
   - 明确标注关系：[实体A] --关系类型--> [实体B]
   - 特别关注隐含的、跨句的关系

3. **结构化输出**：
   - 按主题组织内容
   - 保持逻辑层次清晰
   - 添加关系总结部分

4. **信息增强**：
   - 补充实体的背景信息
   - 明确时间、地点等上下文
   - 保留所有关键细节

原始文本：
{segment}
"""

        messages = [
            {"role": "system", "content": "你是一个专业的知识图谱预处理专家，擅长从文本中识别实体和关系，并进行结构化标注。"},
            {"role": "user", "content": prompt}
        ]

        try:
            result = await self._call_llm(messages)
            return result.strip()
        except Exception as e:
            return f"=== {segment_info} ===\n{segment}"

    def _merge_processed_segments(self, processed_segments: List[str], filename: str = "") -> str:
        """合并处理后的段落"""
        merged_content = f"""# 知识图谱预处理结果
文件：{filename}
处理时间：{time.strftime('%Y-%m-%d %H:%M:%S')}
段落数量：{len(processed_segments)}

## 整体概述
本文档经过分段预处理，每个段落都进行了实体识别、关系标注和结构化整理。

## 处理结果

"""
        
        for i, segment in enumerate(processed_segments, 1):
            merged_content += f"\n### 段落 {i}\n\n{segment}\n\n"
        
        return merged_content

    async def merge_multiple_texts_for_kg(self, texts: List[str], filenames: List[str]) -> str:
        """整合多个文本为知识图谱构建做准备"""
        # 简化版本的多文本整合
        merged_text = f"""# 多文件知识图谱构建
处理时间：{time.strftime('%Y-%m-%d %H:%M:%S')}
文件数量：{len(texts)}

## 文件列表
"""
        for i, filename in enumerate(filenames):
            merged_text += f"{i+1}. {filename}\n"
        
        merged_text += "\n## 整合内容\n\n"
        
        for i, (text, filename) in enumerate(zip(texts, filenames)):
            merged_text += f"### 来源：{filename}\n\n{text[:2000]}\n\n"
        
        return merged_text
