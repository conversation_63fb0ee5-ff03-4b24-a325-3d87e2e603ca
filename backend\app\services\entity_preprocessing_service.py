"""
实体预处理服务
在LLM提取后对实体进行标准化、去重和完整性检查
"""
import json
import asyncio
from typing import List, Dict, Tuple, Optional
from collections import defaultdict
from loguru import logger

from app.core.config import settings
from app.models.knowledge_graph import Entity, Relation
from app.services.llm_service import LLMService
from app.services.entity_standardization_service import EntityStandardizationService
from app.services.file_service import FileService


class EntityPreprocessingService:
    """实体预处理服务"""
    
    def __init__(self):
        self.llm_service = LLMService()
        self.standardization_service = EntityStandardizationService(self.llm_service)
        self.file_service = FileService()
        
        # 全局实体缓存，用于跨文本块的实体一致性
        self.global_entity_cache: Dict[str, Entity] = {}
        self.entity_name_mapping: Dict[str, str] = {}  # 名称到标准名称的映射
    
    async def preprocess_entities_batch(
        self,
        batch_results: List[Tuple[List[Entity], List[Relation]]],
        source_texts: List[str],
        filename: str = ""
    ) -> <PERSON><PERSON>[List[Entity], List[Relation]]:
        """批量预处理实体和关系"""
        logger.info(f"开始批量实体预处理，共 {len(batch_results)} 个批次")
        
        # 1. 收集所有实体和关系
        all_entities = []
        all_relations = []
        
        for entities, relations in batch_results:
            all_entities.extend(entities)
            all_relations.extend(relations)
        
        logger.info(f"收集到 {len(all_entities)} 个实体，{len(all_relations)} 个关系")
        
        # 2. 创建实体摘要临时文件
        if all_entities:
            entity_summary = self.standardization_service.create_entity_summary_file(
                all_entities, filename
            )
            temp_file_path = await self._save_temp_summary(entity_summary, filename)
            logger.info(f"创建实体摘要文件: {temp_file_path}")
        
        # 3. 使用全局上下文进行实体标准化
        combined_source_text = "\n\n".join(source_texts[:3])  # 使用前3个文本块作为上下文
        
        standardized_entities, updated_relations, entity_mapping = await self.standardization_service.standardize_entities(
            all_entities, all_relations, combined_source_text
        )
        
        # 4. 更新全局实体缓存
        self._update_global_cache(standardized_entities, entity_mapping)
        
        # 5. 应用全局一致性检查
        final_entities, final_relations = await self._apply_global_consistency(
            standardized_entities, updated_relations
        )
        
        logger.info(f"实体预处理完成: {len(final_entities)} 个实体，{len(final_relations)} 个关系")
        
        return final_entities, final_relations
    
    async def _save_temp_summary(self, summary_content: str, filename: str) -> str:
        """保存临时摘要文件"""
        import tempfile
        import os
        
        # 创建临时文件
        temp_dir = tempfile.gettempdir()
        temp_filename = f"entity_summary_{filename}_{hash(summary_content) % 10000}.md"
        temp_file_path = os.path.join(temp_dir, temp_filename)
        
        # 写入文件
        with open(temp_file_path, 'w', encoding='utf-8') as f:
            f.write(summary_content)
        
        return temp_file_path
    
    def _update_global_cache(self, entities: List[Entity], entity_mapping: Dict[str, str]):
        """更新全局实体缓存"""
        for entity in entities:
            # 更新实体缓存
            self.global_entity_cache[entity.id] = entity
            
            # 更新名称映射
            self.entity_name_mapping[entity.name.lower()] = entity.name
            
            # 如果有变体名称，也加入映射
            if "variants" in entity.properties:
                for variant in entity.properties["variants"]:
                    self.entity_name_mapping[variant.lower()] = entity.name
    
    async def _apply_global_consistency(
        self, 
        entities: List[Entity], 
        relations: List[Relation]
    ) -> Tuple[List[Entity], List[Relation]]:
        """应用全局一致性检查"""
        if not self.global_entity_cache:
            return entities, relations
        
        logger.info("应用全局实体一致性检查")
        
        # 1. 检查与全局缓存的一致性
        consistent_entities = []
        entity_id_mapping = {}
        
        for entity in entities:
            # 查找是否有全局匹配的实体
            global_match = self._find_global_match(entity)
            
            if global_match:
                # 使用全局实体
                consistent_entities.append(global_match)
                entity_id_mapping[entity.id] = global_match.id
                logger.debug(f"实体全局匹配: {entity.name} -> {global_match.name}")
            else:
                # 添加到全局缓存
                consistent_entities.append(entity)
                entity_id_mapping[entity.id] = entity.id
                self.global_entity_cache[entity.id] = entity
        
        # 2. 更新关系中的实体引用
        consistent_relations = []
        for relation in relations:
            source_id = entity_id_mapping.get(relation.source_entity, relation.source_entity)
            target_id = entity_id_mapping.get(relation.target_entity, relation.target_entity)
            
            updated_relation = Relation(
                id=relation.id,
                source_entity=source_id,
                target_entity=target_id,
                relation_type=relation.relation_type,
                confidence=relation.confidence,
                source_text=relation.source_text,
                properties=relation.properties
            )
            consistent_relations.append(updated_relation)
        
        return consistent_entities, consistent_relations
    
    def _find_global_match(self, entity: Entity) -> Optional[Entity]:
        """在全局缓存中查找匹配的实体"""
        # 1. 精确名称匹配
        for cached_entity in self.global_entity_cache.values():
            if cached_entity.name.lower() == entity.name.lower():
                return cached_entity
        
        # 2. 变体名称匹配
        entity_name_lower = entity.name.lower()
        if entity_name_lower in self.entity_name_mapping:
            standard_name = self.entity_name_mapping[entity_name_lower]
            for cached_entity in self.global_entity_cache.values():
                if cached_entity.name == standard_name:
                    return cached_entity
        
        # 3. 高相似度匹配
        for cached_entity in self.global_entity_cache.values():
            if cached_entity.type == entity.type:
                similarity = self.standardization_service._calculate_entity_similarity(entity, cached_entity)
                if similarity >= 0.9:  # 更高的阈值用于全局匹配
                    return cached_entity
        
        return None
    
    async def preprocess_single_extraction(
        self,
        entities: List[Entity],
        relations: List[Relation],
        source_text: str,
        chunk_id: str = ""
    ) -> Tuple[List[Entity], List[Relation]]:
        """预处理单个文本块的提取结果"""
        if not entities and not relations:
            return [], []
        
        logger.debug(f"预处理文本块 {chunk_id}: {len(entities)} 个实体，{len(relations)} 个关系")
        
        # 1. 基础实体标准化
        standardized_entities, updated_relations, _ = await self.standardization_service.standardize_entities(
            entities, relations, source_text
        )
        
        # 2. 应用全局一致性（如果有全局缓存）
        if self.global_entity_cache:
            final_entities, final_relations = await self._apply_global_consistency(
                standardized_entities, updated_relations
            )
        else:
            final_entities, final_relations = standardized_entities, updated_relations
        
        # 3. 更新全局缓存
        for entity in final_entities:
            self.global_entity_cache[entity.id] = entity
            self.entity_name_mapping[entity.name.lower()] = entity.name
        
        return final_entities, final_relations
    
    def clear_global_cache(self):
        """清空全局缓存（用于新文档处理）"""
        self.global_entity_cache.clear()
        self.entity_name_mapping.clear()
        logger.info("已清空全局实体缓存")
    
    def get_cache_statistics(self) -> Dict[str, int]:
        """获取缓存统计信息"""
        entity_types = defaultdict(int)
        for entity in self.global_entity_cache.values():
            entity_types[entity.type] += 1
        
        return {
            "total_entities": len(self.global_entity_cache),
            "name_mappings": len(self.entity_name_mapping),
            "entity_types": dict(entity_types)
        }
    
    async def validate_entity_completeness(self, entities: List[Entity], source_text: str) -> List[Dict]:
        """验证实体完整性，返回可能不完整的实体列表"""
        incomplete_entities = []
        
        for entity in entities:
            issues = []
            
            # 检查名称长度
            if len(entity.name) < settings.MIN_ENTITY_NAME_LENGTH:
                issues.append("名称过短")
            
            # 检查是否在源文本中存在
            if entity.name not in source_text:
                issues.append("在源文本中未找到")
            
            # 检查是否可能被截断
            if self._is_likely_truncated_entity(entity, source_text):
                issues.append("可能被截断")
            
            # 检查是否缺少描述
            if not entity.description or len(entity.description) < 10:
                issues.append("缺少详细描述")
            
            if issues:
                incomplete_entities.append({
                    "entity": entity.model_dump(),
                    "issues": issues,
                    "suggestions": self._get_completion_suggestions(entity, source_text)
                })
        
        return incomplete_entities
    
    def _is_likely_truncated_entity(self, entity: Entity, source_text: str) -> bool:
        """检查实体是否可能被截断"""
        name = entity.name
        
        # 在源文本中查找更完整的版本
        import re
        
        # 查找以实体名称开头的更长字符串
        pattern = re.escape(name) + r'[^\s，。！？；：]{1,10}'
        matches = re.findall(pattern, source_text)
        
        if matches:
            # 如果找到更长的匹配，说明可能被截断
            longer_matches = [m for m in matches if len(m) > len(name)]
            if longer_matches:
                return True
        
        return False
    
    def _get_completion_suggestions(self, entity: Entity, source_text: str) -> List[str]:
        """获取实体补全建议"""
        suggestions = []
        name = entity.name
        
        # 在源文本中查找可能的完整版本
        import re
        
        # 查找包含实体名称的更长字符串
        pattern = r'[^\s，。！？；：]*' + re.escape(name) + r'[^\s，。！？；：]*'
        matches = re.findall(pattern, source_text)
        
        for match in matches:
            if len(match) > len(name) and match not in suggestions:
                suggestions.append(match)
        
        return suggestions[:3]  # 最多返回3个建议
    
    async def generate_entity_report(self, entities: List[Entity]) -> str:
        """生成实体质量报告"""
        total_entities = len(entities)
        
        # 统计实体类型
        entity_types = defaultdict(int)
        for entity in entities:
            entity_types[entity.type] += 1
        
        # 统计实体质量
        complete_entities = 0
        entities_with_description = 0
        standardized_entities = 0
        
        for entity in entities:
            if len(entity.name) >= settings.MIN_ENTITY_NAME_LENGTH:
                complete_entities += 1
            
            if entity.description and len(entity.description) > 10:
                entities_with_description += 1
            
            if entity.properties.get("standardized"):
                standardized_entities += 1
        
        # 生成报告
        report_lines = [
            "# 实体质量报告",
            "",
            f"## 总体统计",
            f"- 总实体数: {total_entities}",
            f"- 完整实体数: {complete_entities} ({complete_entities/total_entities*100:.1f}%)" if total_entities > 0 else "- 完整实体数: 0",
            f"- 有描述实体数: {entities_with_description} ({entities_with_description/total_entities*100:.1f}%)" if total_entities > 0 else "- 有描述实体数: 0",
            f"- 标准化实体数: {standardized_entities} ({standardized_entities/total_entities*100:.1f}%)" if total_entities > 0 else "- 标准化实体数: 0",
            "",
            "## 实体类型分布",
        ]
        
        for entity_type, count in sorted(entity_types.items(), key=lambda x: x[1], reverse=True):
            percentage = count / total_entities * 100 if total_entities > 0 else 0
            report_lines.append(f"- {entity_type}: {count} ({percentage:.1f}%)")
        
        report_lines.extend([
            "",
            "## 质量评估",
            f"- 实体完整性: {'优秀' if complete_entities/total_entities > 0.9 else '良好' if complete_entities/total_entities > 0.7 else '需要改进'}" if total_entities > 0 else "- 实体完整性: 无数据",
            f"- 描述丰富度: {'优秀' if entities_with_description/total_entities > 0.8 else '良好' if entities_with_description/total_entities > 0.6 else '需要改进'}" if total_entities > 0 else "- 描述丰富度: 无数据",
            f"- 标准化程度: {'优秀' if standardized_entities/total_entities > 0.5 else '良好' if standardized_entities/total_entities > 0.3 else '需要改进'}" if total_entities > 0 else "- 标准化程度: 无数据",
        ])
        
        return "\n".join(report_lines)
