@echo off
title 知识图谱系统 - 前端服务
echo ========================================
echo 启动知识图谱构建系统前端应用
echo ========================================
echo.

cd frontend

echo [1/4] 检查Node.js环境...
node --version
if %errorlevel% neq 0 (
    echo ❌ Node.js未安装或未添加到PATH
    echo 请先安装Node.js: https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js环境正常

echo [2/4] 检查npm...
npm --version
if %errorlevel% neq 0 (
    echo ❌ npm未找到
    pause
    exit /b 1
)
echo ✅ npm正常

echo [3/4] 安装依赖...
if not exist "node_modules" (
    echo 📦 首次运行，安装前端依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
) else (
    echo ✅ 依赖已存在，跳过安装
)

echo [4/4] 启动开发服务器...
echo.
echo ========================================
echo 服务信息
echo ========================================
echo 🌐 前端地址: http://localhost:3000
echo 🔗 后端地址: http://localhost:8000
echo 📚 API文档: http://localhost:8000/docs
echo ========================================
echo.
echo 💡 提示:
echo   - 确保后端服务已启动 (运行 start_backend.bat)
echo   - 前端会自动代理API请求到后端
echo   - 使用 Ctrl+C 停止前端服务
echo.
echo 🚀 正在启动前端服务...
echo.

npm run dev

echo.
echo 👋 前端服务已停止
cd ..
pause
