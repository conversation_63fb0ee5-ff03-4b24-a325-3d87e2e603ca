# 知识图谱构建系统

基于 PocketFlow 架构的智能知识图谱构建和可视化系统，采用大语言模型进行实体关系提取。

## 🚀 功能特性

- 📄 **多格式文件支持**：支持 TXT、PDF、DOCX、XLSX、CSV、JSON 等多种文件格式
- 🤖 **智能实体识别**：基于大语言模型的实体和关系自动提取
- 🔗 **关系推理**：智能识别实体间的复杂关系
- 📊 **可视化展示**：交互式知识图谱可视化界面
- ⚡ **并发处理**：支持大文件的并发处理和批量操作
- 🎯 **实体消歧**：智能实体去重和标准化
- 📈 **进度跟踪**：实时处理进度监控
- 🔄 **图谱管理**：支持图谱的保存、加载、合并和导出
- 🏗️ **PocketFlow 架构**：模块化设计，易于扩展和维护

## 🏗️ 项目架构

本项目采用 PocketFlow 架构设计，具有清晰的模块分离和可复用组件：

```
├── app/                     # 应用核心模块
│   ├── __init__.py         # 包初始化
│   ├── main.py             # FastAPI 应用和端点
│   ├── config.py           # 统一配置管理（前后端）
│   ├── models.py           # Pydantic 请求/响应模型
│   └── simple_flow.py      # 简单 PocketFlow 工作流
├── utils/                   # 可复用工具模块
│   ├── __init__.py         # 工具包初始化
│   ├── file_processor.py   # 文件处理工具
│   ├── llm_service.py      # LLM 服务工具
│   ├── knowledge_graph_builder.py  # 知识图谱构建器
│   └── progress_tracker.py # 进度跟踪工具
├── tests/                   # 测试包
│   └── __init__.py         # 测试初始化
├── docs/                    # 文档目录
├── pyproject.toml          # 项目配置和依赖
├── .env.example            # 环境变量模板
└── README.md               # 项目说明
```

## 🛠️ 技术栈

### 后端核心
- **FastAPI**：现代、快速的 Web 框架
- **Python 3.8+**：主要开发语言
- **Pydantic**：数据验证和设置管理
- **httpx**：异步 HTTP 客户端

### 可复用工具模块
- **文件处理器**：支持多格式文件的文本提取
- **LLM 服务**：统一的大语言模型 API 调用
- **知识图谱构建器**：图谱构建、保存、加载功能
- **进度跟踪器**：任务进度管理和监控

### 前端（保持原有结构）
- **React 18**：用户界面库
- **TypeScript**：类型安全的 JavaScript
- **Ant Design**：UI 组件库
- **D3.js**：数据可视化

## 🚀 快速开始

### 环境要求

- Python 3.8+
- Node.js 16+ (前端开发)

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd knowledge-graph-system
```

2. **安装依赖**
```bash
# 使用 pip 安装
pip install -e .

# 或安装开发依赖
pip install -e ".[dev]"
```

3. **配置环境**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，配置必要参数
# 特别是 LLM_API_KEY 和 LLM_API_BASE
```

4. **启动服务**
```bash
# 启动后端服务
python -m app.main

# 或使用 uvicorn
uvicorn app.main:app --reload
```

5. **访问应用**
- 后端 API：http://localhost:8000
- API 文档：http://localhost:8000/docs
- 健康检查：http://localhost:8000/health

## 📖 使用指南

### 基本工作流

```python
from app.simple_flow import build_kg_from_file, build_kg_from_files

# 从单个文件构建知识图谱
kg = await build_kg_from_file("path/to/your/file.txt", "我的知识图谱")

# 从多个文件构建知识图谱
kg = await build_kg_from_files(
    ["file1.txt", "file2.pdf"], 
    "批量知识图谱"
)
```

### 使用工具模块

```python
from utils import FileProcessor, LLMService, KnowledgeGraphBuilder

# 文件处理
processor = FileProcessor()
file_info = await processor.save_uploaded_file(file_content, "document.pdf")
text = await processor.extract_text_from_file(file_info)

# LLM 服务
llm = LLMService()
entities, relations = await llm.extract_entities_and_relations(text)

# 知识图谱构建
builder = KnowledgeGraphBuilder()
kg = await builder.build_from_text(text, "document.pdf", "我的图谱")
```

### API 使用示例

```bash
# 上传文件
curl -X POST "http://localhost:8000/api/files/upload" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@document.pdf"

# 处理文件
curl -X POST "http://localhost:8000/api/files/process/{file_id}"

# 获取知识图谱列表
curl -X GET "http://localhost:8000/api/kg/list"

# 文本分析
curl -X POST "http://localhost:8000/api/llm/analyze" \
     -H "Content-Type: application/json" \
     -d '{"text": "要分析的文本", "analysis_type": "entities_relations"}'
```

## ⚙️ 配置说明

主要配置项在 `app/config.py` 和 `.env` 文件中：

### LLM 配置
```env
LLM_API_BASE=https://your-llm-api-endpoint
LLM_API_KEY=your-api-key
LLM_MODEL=qwen2.5-32b-instruct
LLM_MAX_TOKENS=4000
LLM_TEMPERATURE=0.1
```

### 处理配置
```env
CHUNK_SIZE=3000
CHUNK_OVERLAP=300
MAX_CONCURRENT_CHUNKS=8
ENABLE_PARALLEL_PROCESSING=true
```

### 文件配置
```env
UPLOAD_DIR=data/uploads
MAX_FILE_SIZE=104857600
ALLOWED_EXTENSIONS=.txt,.pdf,.docx,.doc,.xlsx,.xls,.csv,.json
```

## 🧪 测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_file_processor.py

# 运行测试并生成覆盖率报告
pytest --cov=app --cov=utils
```

## 📦 部署

### 使用 Docker

```bash
# 构建镜像
docker build -t knowledge-graph-system .

# 运行容器
docker run -p 8000:8000 --env-file .env knowledge-graph-system
```

### 生产环境

1. 设置生产环境变量：
```env
DEBUG=false
CORS_ORIGINS=https://yourdomain.com
SECRET_KEY=your-production-secret-key
```

2. 使用生产级 WSGI 服务器：
```bash
pip install gunicorn
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker
```

## 🔧 开发指南

### 添加新的工具模块

1. 在 `utils/` 目录下创建新模块
2. 在 `utils/__init__.py` 中导出新模块
3. 在 `app/main.py` 中使用新模块

### 扩展工作流

1. 在 `app/simple_flow.py` 中添加新的工作流方法
2. 使用现有的工具模块组合功能
3. 添加进度跟踪和错误处理

### 自定义配置

在 `app/config.py` 中添加新的配置项，并在 `.env.example` 中提供示例。

## 🐛 故障排除

### 常见问题

1. **LLM API 调用失败**
   - 检查 `LLM_API_KEY` 和 `LLM_API_BASE` 配置
   - 确认网络连接和 API 配额

2. **文件处理失败**
   - 检查文件格式是否在 `ALLOWED_EXTENSIONS` 中
   - 确认文件大小未超过 `MAX_FILE_SIZE`

3. **模块导入错误**
   - 确保已正确安装项目：`pip install -e .`
   - 检查 Python 路径和虚拟环境

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 启用调试模式
export DEBUG=true
python -m app.main
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 遵循代码规范：
   ```bash
   # 格式化代码
   black .
   isort .
   
   # 类型检查
   mypy .
   
   # 运行测试
   pytest
   ```
4. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
5. 推送到分支 (`git push origin feature/AmazingFeature`)
6. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- 项目主页：[GitHub Repository](https://github.com/your-org/knowledge-graph-system)
- 问题反馈：[Issues](https://github.com/your-org/knowledge-graph-system/issues)
- 邮箱：<EMAIL>

## 📝 更新日志

### v1.0.0 (2024-01-01)
- 🎉 采用 PocketFlow 架构重构
- 🔧 统一配置管理（前后端）
- 📦 模块化工具包设计
- 🚀 简化的工作流 API
- 📖 完善的文档和示例
- 🧪 完整的测试覆盖
- 🐳 Docker 支持
