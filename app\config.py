"""
统一配置管理
包含前后端环境配置
"""
import os
from typing import Optional, List
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # ==================== 应用基本配置 ====================
    APP_NAME: str = "知识图谱构建系统"
    VERSION: str = "1.0.0"
    DEBUG: bool = True
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # ==================== 前端配置 ====================
    FRONTEND_HOST: str = "localhost"
    FRONTEND_PORT: int = 3000
    FRONTEND_BUILD_DIR: str = "frontend/dist"
    
    # ==================== LLM API配置 ====================
    LLM_API_BASE: str = "https://gateway.chat.sensedeal.vip/v1"
    LLM_API_KEY: str = "974fd8d1c155aa3d04b17bf253176b5e"
    LLM_MODEL: str = "qwen2.5-32b-instruct-int4"
    LLM_MAX_TOKENS: int = 4000
    LLM_TEMPERATURE: float = 0.1

    # ==================== 豆包模型配置（用于Wiki生成） ====================
    DOUBAO_MODEL: str = "doubao-seed-1.6"
    DOUBAO_MAX_TOKENS: int = 8000
    DOUBAO_TEMPERATURE: float = 0.3

    # ==================== Wiki生成配置 ====================
    WIKI_GENERATION_MODEL: str = "doubao-seed-1.6"  # 使用豆包模型生成Wiki
    WIKI_OUTPUT_DIR: str = "data/wiki_pages"
    WIKI_TEMPLATE_DIR: str = "templates/wiki"
    ENABLE_WIKI_GENERATION: bool = True
    WIKI_AUTO_UPDATE: bool = True
    WIKI_PARALLEL_PROCESSING: bool = True
    WIKI_MAX_CONCURRENT: int = 5
    
    # ==================== 文件处理配置 ====================
    UPLOAD_DIR: str = "data/uploads"
    MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
    ALLOWED_EXTENSIONS: List[str] = [
        ".txt", ".pdf", ".docx", ".doc", 
        ".xlsx", ".xls", ".csv", ".json"
    ]
    
    # ==================== 知识图谱配置 ====================
    KG_DATA_DIR: str = "data/knowledge_graph"
    MAX_ENTITIES_PER_CHUNK: int = 50
    MAX_RELATIONS_PER_CHUNK: int = 100
    CHUNK_SIZE: int = 3000
    CHUNK_OVERLAP: int = 300
    MAX_CHUNKS_TO_PROCESS: int = 50
    
    # ==================== 并发处理配置 ====================
    MAX_CONCURRENT_CHUNKS: int = 8
    BATCH_SIZE: int = 4
    LLM_REQUEST_TIMEOUT: int = 120
    LLM_MAX_RETRIES: int = 3
    LLM_RETRY_DELAY: float = 1.0
    CONNECTION_POOL_SIZE: int = 20
    ENABLE_PARALLEL_PROCESSING: bool = True
    
    # ==================== 文本预处理配置 ====================
    PREPROCESS_SEGMENT_SIZE: int = 6000
    PREPROCESS_OVERLAP_SIZE: int = 800
    MAX_PREPROCESS_SEGMENTS: int = 10
    ENABLE_ENHANCED_PREPROCESSING: bool = True
    RELATION_EXTRACTION_FOCUS: bool = True
    
    # ==================== 实体处理配置 ====================
    ENABLE_ENTITY_DISAMBIGUATION: bool = True
    ENABLE_LLM_ENTITY_DISAMBIGUATION: bool = True
    ENTITY_SIMILARITY_THRESHOLD: float = 0.8
    LLM_ENTITY_CONFIDENCE_THRESHOLD: float = 0.7
    ENABLE_RELATION_INFERENCE: bool = True
    ENABLE_CONNECTIVITY_CHECK: bool = True
    MAX_ENTITY_VARIANTS: int = 5
    FUZZY_MATCH_THRESHOLD: float = 0.7
    
    # ==================== 实体标准化配置 ====================
    ENABLE_ENTITY_STANDARDIZATION: bool = True
    ENTITY_COMPLETION_THRESHOLD: float = 0.85
    ENABLE_ENTITY_COMPLETION: bool = True
    ENTITY_TEMP_FILE_FORMAT: str = "structured"
    MIN_ENTITY_NAME_LENGTH: int = 2
    MAX_ENTITY_NAME_LENGTH: int = 50
    ENABLE_ENTITY_VALIDATION: bool = True
    
    # ==================== 日志配置 ====================
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    
    # ==================== 数据库配置 ====================
    DATABASE_URL: Optional[str] = None
    REDIS_URL: Optional[str] = None
    
    # ==================== 安全配置 ====================
    SECRET_KEY: str = "your-secret-key-here"
    CORS_ORIGINS: List[str] = ["*"]
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()

# 确保必要的目录存在
os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
os.makedirs(settings.KG_DATA_DIR, exist_ok=True)
os.makedirs("logs", exist_ok=True)
