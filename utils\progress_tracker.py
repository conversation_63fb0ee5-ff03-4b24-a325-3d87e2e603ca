"""
进度跟踪器工具模块
提供任务进度跟踪、状态管理等功能
"""
import asyncio
import time
from datetime import datetime
from typing import Dict, List, Optional, Any

from app.models import ProgressInfo, ProcessStatus


class ProgressTracker:
    """进度跟踪器类"""
    
    def __init__(self):
        self._tasks: Dict[str, ProgressInfo] = {}
        self._lock = asyncio.Lock()
    
    async def start_task(
        self,
        task_id: str,
        task_name: str = "",
        total_steps: int = 1,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """开始一个新任务"""
        async with self._lock:
            progress_info = ProgressInfo(
                task_id=task_id,
                status=ProcessStatus.PROCESSING,
                progress=0.0,
                current_step=task_name or "开始处理",
                total_steps=total_steps,
                completed_steps=0,
                start_time=datetime.now()
            )
            
            # 添加元数据
            if metadata:
                progress_info.metadata = metadata
            
            self._tasks[task_id] = progress_info
    
    async def update_progress(
        self,
        task_id: str,
        step: Optional[str] = None,
        progress: Optional[float] = None,
        completed_steps: Optional[int] = None,
        estimated_time: Optional[float] = None
    ) -> bool:
        """更新任务进度"""
        async with self._lock:
            if task_id not in self._tasks:
                return False
            
            task = self._tasks[task_id]
            
            # 更新步骤描述
            if step is not None:
                task.current_step = step
            
            # 更新进度百分比
            if progress is not None:
                task.progress = min(max(progress, 0.0), 1.0)
            
            # 更新完成步骤数
            if completed_steps is not None:
                task.completed_steps = completed_steps
                # 根据完成步骤数计算进度
                if task.total_steps > 0:
                    task.progress = task.completed_steps / task.total_steps
            
            # 更新预估时间
            if estimated_time is not None:
                task.estimated_time = estimated_time
            else:
                # 自动计算预估时间
                task.estimated_time = self._calculate_estimated_time(task)
            
            return True
    
    async def complete_task(
        self,
        task_id: str,
        success: bool = True,
        error: Optional[str] = None
    ) -> bool:
        """完成任务"""
        async with self._lock:
            if task_id not in self._tasks:
                return False
            
            task = self._tasks[task_id]
            
            if success:
                task.status = ProcessStatus.COMPLETED
                task.progress = 1.0
                task.completed_steps = task.total_steps
                task.current_step = "处理完成"
            else:
                task.status = ProcessStatus.FAILED
                task.error_message = error or "处理失败"
                task.current_step = "处理失败"
            
            task.estimated_time = 0.0
            
            return True
    
    async def cancel_task(self, task_id: str, reason: str = "") -> bool:
        """取消任务"""
        async with self._lock:
            if task_id not in self._tasks:
                return False
            
            task = self._tasks[task_id]
            task.status = ProcessStatus.FAILED  # 使用FAILED状态表示取消
            task.error_message = f"任务已取消: {reason}" if reason else "任务已取消"
            task.current_step = "已取消"
            task.estimated_time = 0.0
            
            return True
    
    async def get_progress(self, task_id: str) -> Optional[ProgressInfo]:
        """获取任务进度"""
        async with self._lock:
            return self._tasks.get(task_id)
    
    async def list_active_tasks(self) -> List[ProgressInfo]:
        """列出所有活跃任务"""
        async with self._lock:
            active_tasks = []
            for task in self._tasks.values():
                if task.status == ProcessStatus.PROCESSING:
                    active_tasks.append(task)
            return active_tasks
    
    async def list_all_tasks(self) -> List[ProgressInfo]:
        """列出所有任务"""
        async with self._lock:
            return list(self._tasks.values())
    
    async def remove_task(self, task_id: str) -> bool:
        """移除任务记录"""
        async with self._lock:
            if task_id in self._tasks:
                del self._tasks[task_id]
                return True
            return False
    
    async def cleanup_completed_tasks(self, max_age_hours: int = 24) -> int:
        """清理已完成的旧任务"""
        async with self._lock:
            current_time = datetime.now()
            tasks_to_remove = []
            
            for task_id, task in self._tasks.items():
                if task.status in [ProcessStatus.COMPLETED, ProcessStatus.FAILED]:
                    # 计算任务年龄
                    age_hours = (current_time - task.start_time).total_seconds() / 3600
                    if age_hours > max_age_hours:
                        tasks_to_remove.append(task_id)
            
            # 移除旧任务
            for task_id in tasks_to_remove:
                del self._tasks[task_id]
            
            return len(tasks_to_remove)
    
    def _calculate_estimated_time(self, task: ProgressInfo) -> float:
        """计算预估剩余时间（秒）"""
        if task.progress <= 0:
            return 0.0
        
        # 计算已用时间
        elapsed_time = (datetime.now() - task.start_time).total_seconds()
        
        # 根据当前进度估算总时间
        if task.progress > 0:
            estimated_total_time = elapsed_time / task.progress
            remaining_time = estimated_total_time - elapsed_time
            return max(remaining_time, 0.0)
        
        return 0.0
    
    async def get_task_statistics(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        async with self._lock:
            stats = {
                "total_tasks": len(self._tasks),
                "active_tasks": 0,
                "completed_tasks": 0,
                "failed_tasks": 0,
                "average_completion_time": 0.0
            }
            
            completion_times = []
            
            for task in self._tasks.values():
                if task.status == ProcessStatus.PROCESSING:
                    stats["active_tasks"] += 1
                elif task.status == ProcessStatus.COMPLETED:
                    stats["completed_tasks"] += 1
                    # 计算完成时间
                    completion_time = (datetime.now() - task.start_time).total_seconds()
                    completion_times.append(completion_time)
                elif task.status == ProcessStatus.FAILED:
                    stats["failed_tasks"] += 1
            
            # 计算平均完成时间
            if completion_times:
                stats["average_completion_time"] = sum(completion_times) / len(completion_times)
            
            return stats
    
    async def increment_step(self, task_id: str, step_name: str = "") -> bool:
        """增加一个完成步骤"""
        async with self._lock:
            if task_id not in self._tasks:
                return False
            
            task = self._tasks[task_id]
            task.completed_steps += 1
            
            if step_name:
                task.current_step = step_name
            
            # 更新进度
            if task.total_steps > 0:
                task.progress = min(task.completed_steps / task.total_steps, 1.0)
            
            # 更新预估时间
            task.estimated_time = self._calculate_estimated_time(task)
            
            return True
    
    async def set_total_steps(self, task_id: str, total_steps: int) -> bool:
        """设置任务总步骤数"""
        async with self._lock:
            if task_id not in self._tasks:
                return False
            
            task = self._tasks[task_id]
            task.total_steps = total_steps
            
            # 重新计算进度
            if total_steps > 0:
                task.progress = min(task.completed_steps / total_steps, 1.0)
            
            return True
    
    async def add_task_metadata(
        self,
        task_id: str,
        key: str,
        value: Any
    ) -> bool:
        """添加任务元数据"""
        async with self._lock:
            if task_id not in self._tasks:
                return False
            
            task = self._tasks[task_id]
            if not hasattr(task, 'metadata') or task.metadata is None:
                task.metadata = {}
            
            task.metadata[key] = value
            return True
    
    async def get_task_metadata(
        self,
        task_id: str,
        key: Optional[str] = None
    ) -> Any:
        """获取任务元数据"""
        async with self._lock:
            if task_id not in self._tasks:
                return None
            
            task = self._tasks[task_id]
            if not hasattr(task, 'metadata') or task.metadata is None:
                return None
            
            if key is None:
                return task.metadata
            else:
                return task.metadata.get(key)


# 创建全局进度跟踪器实例
global_progress_tracker = ProgressTracker()


# 便捷函数
async def start_task(task_id: str, task_name: str = "", total_steps: int = 1) -> None:
    """开始任务的便捷函数"""
    await global_progress_tracker.start_task(task_id, task_name, total_steps)


async def update_progress(task_id: str, step: str = "", progress: float = None) -> bool:
    """更新进度的便捷函数"""
    return await global_progress_tracker.update_progress(task_id, step, progress)


async def complete_task(task_id: str, success: bool = True, error: str = None) -> bool:
    """完成任务的便捷函数"""
    return await global_progress_tracker.complete_task(task_id, success, error)


async def get_progress(task_id: str) -> Optional[ProgressInfo]:
    """获取进度的便捷函数"""
    return await global_progress_tracker.get_progress(task_id)
