"""
知识图谱构建系统 - FastAPI 主应用
基于 PocketFlow 架构
"""
import uvicorn
from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import J<PERSON><PERSON><PERSON>ponse
from typing import List, Optional
import asyncio
from datetime import datetime

from .config import settings
from .models import (
    TextAnalysisRequest, TextAnalysisResponse,
    FileUploadResponse, ProcessResult, KnowledgeGraphResponse,
    ProgressInfo, BaseResponse
)
from utils.file_processor import FileProcessor
from utils.llm_service import LLMService
from utils.knowledge_graph_builder import KnowledgeGraphBuilder
from utils.progress_tracker import ProgressTracker


# 创建FastAPI应用
app = FastAPI(
    title=settings.APP_NAME,
    description="基于大语言模型的智能知识图谱构建和可视化系统",
    version=settings.VERSION,
    debug=settings.DEBUG
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务
app.mount("/uploads", StaticFiles(directory=settings.UPLOAD_DIR), name="uploads")

# 初始化服务
file_processor = FileProcessor()
llm_service = LLMService()
kg_builder = KnowledgeGraphBuilder()
progress_tracker = ProgressTracker()


# ==================== 基础路由 ====================
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": f"{settings.APP_NAME} API",
        "version": settings.VERSION,
        "docs": "/docs",
        "timestamp": datetime.now().isoformat()
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": settings.VERSION
    }


# ==================== 文件处理路由 ====================
@app.post("/api/files/upload", response_model=FileUploadResponse)
async def upload_file(file: UploadFile = File(...)):
    """上传文件"""
    try:
        # 读取文件内容
        file_content = await file.read()
        
        # 保存文件
        file_info = await file_processor.save_uploaded_file(file_content, file.filename)
        
        return FileUploadResponse(
            success=True,
            message="文件上传成功",
            file_info=file_info
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@app.post("/api/files/process/{file_id}")
async def process_file(file_id: str):
    """处理单个文件"""
    try:
        # 获取文件信息
        file_info = await file_processor.get_file_info(file_id)
        if not file_info:
            raise HTTPException(status_code=404, detail="文件未找到")
        
        # 提取文本
        text = await file_processor.extract_text_from_file(file_info)
        
        # 生成任务ID和图谱名称
        task_id = f"process_{file_id}"
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        kg_name = f"KG_{timestamp}"
        
        # 构建知识图谱
        kg = await kg_builder.build_from_text(
            text,
            file_info.original_name,
            kg_name,
            task_id=task_id
        )
        
        # 创建处理结果
        chunks = file_processor.split_text_into_chunks(text)
        result = ProcessResult(
            file_id=file_id,
            status="completed",
            text_chunks=chunks,
            extracted_entities=[entity.model_dump() for entity in kg.entities],
            extracted_relations=[relation.model_dump() for relation in kg.relations]
        )
        
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/files/batch-process")
async def batch_process_files(file_ids: List[str]):
    """批量处理文件"""
    try:
        texts = []
        filenames = []
        
        # 提取所有文件的文本
        for file_id in file_ids:
            file_info = await file_processor.get_file_info(file_id)
            if file_info:
                text = await file_processor.extract_text_from_file(file_info)
                texts.append(text)
                filenames.append(file_info.original_name)
        
        if not texts:
            raise HTTPException(status_code=404, detail="未找到有效文件")
        
        # 生成图谱名称
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        kg_name = f"KG_Batch_{timestamp}"
        
        # 构建知识图谱
        kg = await kg_builder.build_from_multiple_texts(texts, filenames, kg_name)
        
        return KnowledgeGraphResponse(
            success=True,
            message="批量处理完成",
            knowledge_graph=kg
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# ==================== LLM 分析路由 ====================
@app.post("/api/llm/analyze", response_model=TextAnalysisResponse)
async def analyze_text(request: TextAnalysisRequest):
    """分析文本"""
    try:
        if request.analysis_type == "entities_relations":
            entities, relations = await llm_service.extract_entities_and_relations(request.text)
            result = {
                "entities": [entity.model_dump() for entity in entities],
                "relations": [relation.model_dump() for relation in relations]
            }
        elif request.analysis_type == "summary":
            summary = await llm_service.summarize_text(request.text)
            result = {"summary": summary}
        else:
            raise HTTPException(status_code=400, detail="不支持的分析类型")
        
        return TextAnalysisResponse(
            success=True,
            analysis_type=request.analysis_type,
            result=result,
            message="分析完成"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# ==================== 知识图谱路由 ====================
@app.get("/api/kg/list")
async def list_knowledge_graphs():
    """获取知识图谱列表"""
    try:
        graphs = await kg_builder.list_knowledge_graphs()
        return {"success": True, "graphs": graphs}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/kg/{kg_id}")
async def get_knowledge_graph(kg_id: str):
    """获取指定知识图谱"""
    try:
        kg = await kg_builder.load_knowledge_graph(kg_id)
        if not kg:
            raise HTTPException(status_code=404, detail="知识图谱未找到")
        
        return KnowledgeGraphResponse(
            success=True,
            message="获取成功",
            knowledge_graph=kg
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# ==================== 进度跟踪路由 ====================
@app.get("/api/progress/{task_id}")
async def get_progress(task_id: str):
    """获取任务进度"""
    try:
        progress = await progress_tracker.get_progress(task_id)
        if not progress:
            raise HTTPException(status_code=404, detail="任务未找到")
        
        return {"success": True, "progress": progress}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# ==================== 错误处理 ====================
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理"""
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "服务器内部错误",
            "detail": str(exc) if settings.DEBUG else "请联系管理员",
            "timestamp": datetime.now().isoformat()
        }
    )


# ==================== 应用启动 ====================
if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
