"""
知识图谱构建系统 - FastAPI 主应用
基于 PocketFlow 架构
"""
import uvicorn
import webbrowser
import threading
import time
import json
import httpx
from pathlib import Path
from fastapi import FastAPI, HTTPException, UploadFile, File, Form, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
from typing import List, Optional, Dict, Any
import asyncio
from datetime import datetime

from .config import settings
from .models import (
    TextAnalysisRequest, TextAnalysisResponse,
    FileUploadResponse, ProcessResult, KnowledgeGraphResponse,
    ProgressInfo, BaseResponse, BatchProcessRequest, FolderScanResult,
    EntityClassificationRequest, WikiPageInfo, WikiGenerationRequest,
    WikiGenerationResponse, WikiContent
)
from utils.file_processor import FileProcessor
from utils.llm_service import LLMService
from utils.knowledge_graph_builder import KnowledgeGraphBuilder
from utils.progress_tracker import ProgressTracker
from utils.wiki_generator import WikiGenerator


# 创建FastAPI应用
app = FastAPI(
    title=settings.APP_NAME,
    description="基于大语言模型的智能知识图谱构建和可视化系统",
    version=settings.VERSION,
    debug=settings.DEBUG
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务
app.mount("/uploads", StaticFiles(directory=settings.UPLOAD_DIR), name="uploads")
app.mount("/wiki", StaticFiles(directory=settings.WIKI_OUTPUT_DIR), name="wiki")

# 初始化服务
file_processor = FileProcessor()
llm_service = LLMService()
kg_builder = KnowledgeGraphBuilder()
progress_tracker = ProgressTracker()
wiki_generator = WikiGenerator()


# ==================== 基础路由 ====================
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": f"{settings.APP_NAME} API",
        "version": settings.VERSION,
        "docs": "/docs",
        "timestamp": datetime.now().isoformat()
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": settings.VERSION
    }


@app.get("/api/system/status")
async def system_status():
    """系统状态检查"""
    # 检查前端是否可用
    frontend_available = await check_frontend_availability()

    return {
        "backend": {
            "status": "running",
            "host": settings.HOST,
            "port": settings.PORT,
            "version": settings.VERSION
        },
        "frontend": {
            "status": "running" if frontend_available else "not_running",
            "host": settings.FRONTEND_HOST,
            "port": settings.FRONTEND_PORT,
            "url": f"http://{settings.FRONTEND_HOST}:{settings.FRONTEND_PORT}"
        },
        "services": {
            "wiki_generation": settings.ENABLE_WIKI_GENERATION,
            "parallel_processing": settings.ENABLE_PARALLEL_PROCESSING,
            "llm_model": settings.LLM_MODEL,
            "wiki_model": settings.DOUBAO_MODEL
        },
        "urls": {
            "frontend": f"http://{settings.FRONTEND_HOST}:{settings.FRONTEND_PORT}",
            "backend": f"http://{settings.HOST}:{settings.PORT}",
            "api_docs": f"http://{settings.HOST}:{settings.PORT}/docs"
        }
    }


# ==================== 文件处理路由 ====================
@app.post("/api/files/upload", response_model=FileUploadResponse)
async def upload_file(file: UploadFile = File(...)):
    """上传文件"""
    try:
        # 读取文件内容
        file_content = await file.read()
        
        # 保存文件
        file_info = await file_processor.save_uploaded_file(file_content, file.filename)
        
        return FileUploadResponse(
            success=True,
            message="文件上传成功",
            file_info=file_info
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@app.post("/api/files/process/{file_id}")
async def process_file(file_id: str):
    """处理单个文件"""
    try:
        # 获取文件信息
        file_info = await file_processor.get_file_info(file_id)
        if not file_info:
            raise HTTPException(status_code=404, detail="文件未找到")
        
        # 提取文本
        text = await file_processor.extract_text_from_file(file_info)
        
        # 生成任务ID和图谱名称
        task_id = f"process_{file_id}"
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        kg_name = f"KG_{timestamp}"
        
        # 构建知识图谱
        kg = await kg_builder.build_from_text(
            text,
            file_info.original_name,
            kg_name,
            task_id=task_id
        )
        
        # 创建处理结果
        chunks = file_processor.split_text_into_chunks(text)
        result = ProcessResult(
            file_id=file_id,
            status="completed",
            text_chunks=chunks,
            extracted_entities=[entity.model_dump() for entity in kg.entities],
            extracted_relations=[relation.model_dump() for relation in kg.relations]
        )
        
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/files/batch-process")
async def batch_process_files(file_ids: List[str]):
    """批量处理文件"""
    try:
        texts = []
        filenames = []
        
        # 提取所有文件的文本
        for file_id in file_ids:
            file_info = await file_processor.get_file_info(file_id)
            if file_info:
                text = await file_processor.extract_text_from_file(file_info)
                texts.append(text)
                filenames.append(file_info.original_name)
        
        if not texts:
            raise HTTPException(status_code=404, detail="未找到有效文件")
        
        # 生成图谱名称
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        kg_name = f"KG_Batch_{timestamp}"
        
        # 构建知识图谱
        kg = await kg_builder.build_from_multiple_texts(texts, filenames, kg_name)
        
        return KnowledgeGraphResponse(
            success=True,
            message="批量处理完成",
            knowledge_graph=kg
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# ==================== LLM 分析路由 ====================
@app.post("/api/llm/analyze", response_model=TextAnalysisResponse)
async def analyze_text(request: TextAnalysisRequest):
    """分析文本"""
    try:
        if request.analysis_type == "entities_relations":
            entities, relations = await llm_service.extract_entities_and_relations(request.text)
            result = {
                "entities": [entity.model_dump() for entity in entities],
                "relations": [relation.model_dump() for relation in relations]
            }
        elif request.analysis_type == "summary":
            summary = await llm_service.summarize_text(request.text)
            result = {"summary": summary}
        else:
            raise HTTPException(status_code=400, detail="不支持的分析类型")
        
        return TextAnalysisResponse(
            success=True,
            analysis_type=request.analysis_type,
            result=result,
            message="分析完成"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# ==================== 知识图谱路由 ====================
@app.get("/api/kg/list")
async def list_knowledge_graphs():
    """获取知识图谱列表"""
    try:
        graphs = await kg_builder.list_knowledge_graphs()
        return {"success": True, "graphs": graphs}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/kg/{kg_id}")
async def get_knowledge_graph(kg_id: str):
    """获取指定知识图谱"""
    try:
        kg = await kg_builder.load_knowledge_graph(kg_id)
        if not kg:
            raise HTTPException(status_code=404, detail="知识图谱未找到")
        
        return KnowledgeGraphResponse(
            success=True,
            message="获取成功",
            knowledge_graph=kg
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# ==================== 进度跟踪路由 ====================
@app.get("/api/progress/{task_id}")
async def get_progress(task_id: str):
    """获取任务进度"""
    try:
        progress = await progress_tracker.get_progress(task_id)
        if not progress:
            raise HTTPException(status_code=404, detail="任务未找到")

        return {"success": True, "progress": progress}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/progress/list/active")
async def list_active_tasks():
    """列出所有活跃任务"""
    try:
        tasks = await progress_tracker.list_active_tasks()
        return {"success": True, "tasks": tasks}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# ==================== 扩展的文件处理路由 ====================
@app.post("/api/files/batch-process-advanced")
async def batch_process_files_advanced(request: BatchProcessRequest):
    """高级批量处理文件"""
    try:
        texts = []
        filenames = []

        # 提取所有文件的文本
        for file_id in request.file_ids:
            file_info = await file_processor.get_file_info(file_id)
            if file_info:
                text = await file_processor.extract_text_from_file(file_info)
                texts.append(text)
                filenames.append(file_info.original_name)

        if not texts:
            raise HTTPException(status_code=404, detail="未找到有效文件")

        # 生成图谱名称
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        kg_name = f"KG_Batch_{timestamp}"

        # 构建知识图谱
        kg = await kg_builder.build_from_multiple_texts(texts, filenames, kg_name)

        # 创建处理结果
        result = ProcessResult(
            file_id=f"batch_{len(request.file_ids)}_files",
            status="completed",
            text_chunks=[],
            extracted_entities=[entity.model_dump() for entity in kg.entities],
            extracted_relations=[relation.model_dump() for relation in kg.relations]
        )

        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/files/supported-formats")
async def get_supported_formats():
    """获取支持的文件格式"""
    return {
        "supported_extensions": settings.ALLOWED_EXTENSIONS,
        "max_file_size": settings.MAX_FILE_SIZE,
        "max_file_size_mb": settings.MAX_FILE_SIZE / (1024 * 1024)
    }


@app.post("/api/files/cleanup-uploads")
async def cleanup_uploads():
    """手动清理uploads目录中的所有文件"""
    try:
        deleted_count = await file_processor.cleanup_uploads_directory()
        return {
            "success": True,
            "deleted_count": deleted_count,
            "message": f"成功清理了 {deleted_count} 个文件"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# ==================== 扩展的知识图谱路由 ====================
@app.post("/api/kg/{kg_id}/merge/{other_kg_id}")
async def merge_knowledge_graphs(kg_id: str, other_kg_id: str):
    """合并两个知识图谱"""
    try:
        # 加载两个图谱
        kg1 = await kg_builder.load_knowledge_graph(kg_id)
        kg2 = await kg_builder.load_knowledge_graph(other_kg_id)

        if not kg1 or not kg2:
            raise HTTPException(status_code=404, detail="知识图谱不存在")

        # 合并图谱
        merged_kg = await kg_builder.merge_knowledge_graphs([kg_id, other_kg_id], f"{kg1.name}_merged_{kg2.name}")

        return {
            "success": True,
            "message": "图谱合并成功",
            "merged_kg_id": merged_kg.id,
            "entity_count": len(merged_kg.entities),
            "relation_count": len(merged_kg.relations)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/api/kg/{kg_id}")
async def delete_knowledge_graph(kg_id: str):
    """删除知识图谱"""
    try:
        success = await kg_builder.delete_knowledge_graph(kg_id)
        if success:
            return {"success": True, "message": "知识图谱删除成功"}
        else:
            raise HTTPException(status_code=404, detail="知识图谱未找到")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/kg/{kg_id}/stats")
async def get_knowledge_graph_stats(kg_id: str):
    """获取知识图谱统计信息"""
    try:
        kg = await kg_builder.load_knowledge_graph(kg_id)
        if not kg:
            raise HTTPException(status_code=404, detail="知识图谱未找到")

        stats = kg_builder.get_graph_statistics(kg)
        return {"success": True, "stats": stats}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/kg/{kg_id}/export")
async def export_knowledge_graph(kg_id: str, format: str = Query("json", description="导出格式")):
    """导出知识图谱"""
    try:
        exported_data = await kg_builder.export_knowledge_graph(kg_id, format)
        if exported_data is None:
            raise HTTPException(status_code=404, detail="知识图谱未找到或格式不支持")

        return {"success": True, "data": exported_data, "format": format}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# ==================== Wiki 页面路由 ====================
@app.post("/api/wiki/generate", response_model=WikiGenerationResponse)
async def generate_wiki_page(request: WikiGenerationRequest):
    """生成知识图谱的Wiki页面"""
    try:
        start_time = time.time()

        # 加载知识图谱
        kg = await kg_builder.load_knowledge_graph(request.kg_id)
        if not kg:
            raise HTTPException(status_code=404, detail="知识图谱未找到")

        # 检查是否已存在Wiki页面
        existing_url = await wiki_generator.get_wiki_url(request.kg_id)
        if existing_url and not request.force_regenerate:
            generation_time = time.time() - start_time
            return WikiGenerationResponse(
                success=True,
                message="Wiki页面已存在",
                wiki_url=existing_url,
                generation_time=generation_time,
                entity_count=len(kg.entities)
            )

        # 生成Wiki页面
        wiki_file_path = await wiki_generator.save_wiki_page(kg)
        wiki_url = f"/wiki/{request.kg_id}.html"

        generation_time = time.time() - start_time

        return WikiGenerationResponse(
            success=True,
            message="Wiki页面生成成功",
            wiki_url=wiki_url,
            generation_time=generation_time,
            entity_count=len(kg.entities)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/wiki/list")
async def list_wiki_pages():
    """获取所有Wiki页面列表"""
    try:
        wiki_pages = await wiki_generator.list_wiki_pages()
        return {"success": True, "wiki_pages": wiki_pages}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/wiki/{kg_id}")
async def get_wiki_page_info(kg_id: str):
    """获取Wiki页面信息"""
    try:
        wiki_url = await wiki_generator.get_wiki_url(kg_id)
        if not wiki_url:
            raise HTTPException(status_code=404, detail="Wiki页面未找到")

        # 读取Wiki数据
        wiki_data_path = Path(settings.WIKI_OUTPUT_DIR) / f"{kg_id}.json"
        if wiki_data_path.exists():
            with open(wiki_data_path, 'r', encoding='utf-8') as f:
                wiki_data = json.load(f)

            return {
                "success": True,
                "wiki_info": {
                    "kg_id": kg_id,
                    "kg_name": wiki_data.get("kg_name", ""),
                    "url": wiki_url,
                    "generated_at": wiki_data.get("generated_at", ""),
                    "last_updated": wiki_data.get("last_updated", ""),
                    "entity_count": wiki_data.get("statistics", {}).get("total_entities", 0),
                    "relation_count": wiki_data.get("statistics", {}).get("total_relations", 0)
                }
            }
        else:
            return {
                "success": True,
                "wiki_info": {
                    "kg_id": kg_id,
                    "url": wiki_url,
                    "entity_count": 0,
                    "relation_count": 0
                }
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/wiki/{kg_id}/content")
async def get_wiki_content(kg_id: str):
    """获取Wiki页面内容（JSON格式）"""
    try:
        wiki_data_path = Path(settings.WIKI_OUTPUT_DIR) / f"{kg_id}.json"
        if not wiki_data_path.exists():
            raise HTTPException(status_code=404, detail="Wiki内容未找到")

        with open(wiki_data_path, 'r', encoding='utf-8') as f:
            wiki_data = json.load(f)

        return {"success": True, "content": wiki_data}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.put("/api/wiki/{kg_id}/update")
async def update_wiki_page(kg_id: str):
    """更新Wiki页面"""
    try:
        start_time = time.time()

        # 加载知识图谱
        kg = await kg_builder.load_knowledge_graph(kg_id)
        if not kg:
            raise HTTPException(status_code=404, detail="知识图谱未找到")

        # 更新Wiki页面
        wiki_file_path = await wiki_generator.update_wiki_page(kg)
        wiki_url = f"/wiki/{kg_id}.html"

        generation_time = time.time() - start_time

        return {
            "success": True,
            "message": "Wiki页面更新成功",
            "wiki_url": wiki_url,
            "update_time": generation_time,
            "entity_count": len(kg.entities)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/api/wiki/{kg_id}")
async def delete_wiki_page(kg_id: str):
    """删除Wiki页面"""
    try:
        success = await wiki_generator.delete_wiki_page(kg_id)
        if success:
            return {"success": True, "message": "Wiki页面删除成功"}
        else:
            raise HTTPException(status_code=404, detail="Wiki页面未找到")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# ==================== 扩展的LLM路由 ====================
@app.post("/api/llm/classify-entity")
async def classify_entity(request: EntityClassificationRequest):
    """实体分类"""
    try:
        entity_type = await llm_service.classify_entity_type(request.entity_name, request.context)
        return {
            "success": True,
            "entity_name": request.entity_name,
            "entity_type": entity_type
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/llm/models")
async def get_available_models():
    """获取可用的模型列表"""
    try:
        import httpx

        headers = {
            "Authorization": f"Bearer {settings.LLM_API_KEY}",
            "Content-Type": "application/json"
        }

        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{settings.LLM_API_BASE}/models",
                headers=headers
            )
            response.raise_for_status()
            return response.json()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模型列表失败: {str(e)}")


@app.get("/api/llm/config")
async def get_llm_config():
    """获取LLM配置信息"""
    return {
        "api_base": settings.LLM_API_BASE,
        "model": settings.LLM_MODEL,
        "max_tokens": settings.LLM_MAX_TOKENS,
        "temperature": settings.LLM_TEMPERATURE
    }


@app.post("/api/llm/test-connection")
async def test_llm_connection():
    """测试LLM连接"""
    try:
        # 发送一个简单的测试请求
        test_messages = [
            {"role": "user", "content": "Hello, please respond with 'Connection successful'"}
        ]

        response = await llm_service._call_llm(test_messages)

        return {
            "success": True,
            "message": "连接成功",
            "response": response
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"连接失败: {str(e)}"
        }


# ==================== 自动打开前端网页功能 ====================
async def check_frontend_availability():
    """检查前端服务是否可用"""
    frontend_url = f"http://{settings.FRONTEND_HOST}:{settings.FRONTEND_PORT}"
    try:
        async with httpx.AsyncClient(timeout=5.0) as client:
            response = await client.get(frontend_url)
            return response.status_code == 200
    except:
        return False

def open_frontend_browser():
    """在后台线程中打开前端网页"""
    time.sleep(2)  # 等待服务器启动

    frontend_url = f"http://{settings.FRONTEND_HOST}:{settings.FRONTEND_PORT}"
    backend_url = f"http://{settings.HOST}:{settings.PORT}"

    try:
        # 直接打开前端页面，不管前端服务是否启动
        webbrowser.open(frontend_url)
        print(f"🌐 已自动打开前端网页: {frontend_url}")

        # 在后台检查前端状态并给出提示
        try:
            import requests
            response = requests.get(frontend_url, timeout=2)
            if response.status_code == 200:
                print(f"✅ 前端服务运行正常")
            else:
                print(f"⚠️ 前端服务状态异常 (状态码: {response.status_code})")
                print(f"💡 如果页面无法加载，请运行: start_frontend.bat")
        except requests.exceptions.ConnectionError:
            print(f"⚠️ 前端服务未启动，页面可能无法加载")
            print(f"💡 请运行以下命令启动前端:")
            print(f"   start_frontend.bat")
            print(f"📚 或访问后端API文档: {backend_url}/docs")
        except requests.exceptions.Timeout:
            print(f"⚠️ 前端服务响应超时")
        except Exception as e:
            print(f"⚠️ 检查前端状态时出错: {e}")

    except Exception as e:
        print(f"❌ 无法自动打开浏览器: {e}")
        print(f"🌐 请手动访问前端: {frontend_url}")
        print(f"🔗 或访问后端: {backend_url}")
        print(f"📚 API文档: {backend_url}/docs")


# ==================== 错误处理 ====================
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理"""
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "服务器内部错误",
            "detail": str(exc) if settings.DEBUG else "请联系管理员",
            "timestamp": datetime.now().isoformat()
        }
    )


# ==================== 应用启动 ====================
def main():
    """主函数"""
    print("=" * 60)
    print(f"🚀 启动 {settings.APP_NAME} v{settings.VERSION}")
    print("=" * 60)
    print(f"📡 后端服务: http://{settings.HOST}:{settings.PORT}")
    print(f"📚 API文档: http://{settings.HOST}:{settings.PORT}/docs")
    print(f"🌐 前端地址: http://{settings.FRONTEND_HOST}:{settings.FRONTEND_PORT}")
    print(f"🔧 系统状态: http://{settings.HOST}:{settings.PORT}/api/system/status")
    print("=" * 60)
    print("💡 提示:")
    print("  - 系统将自动打开前端页面")
    print("  - 如果前端未启动，页面可能无法加载")
    print("  - 要启动前端，请运行: start_frontend.bat")
    print("  - 要同时启动前后端，请运行: start_full_system.bat")
    print("=" * 60)

    # 在后台线程中打开前端网页
    browser_thread = threading.Thread(target=open_frontend_browser, daemon=True)
    browser_thread.start()

    try:
        uvicorn.run(
            "app.main:app",
            host=settings.HOST,
            port=settings.PORT,
            reload=settings.DEBUG,
            log_level=settings.LOG_LEVEL.lower()
        )
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"\n❌ 服务启动失败: {e}")
        print("💡 请检查端口是否被占用或配置是否正确")


if __name__ == "__main__":
    main()
