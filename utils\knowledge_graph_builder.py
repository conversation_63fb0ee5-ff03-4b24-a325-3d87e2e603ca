"""
知识图谱构建器工具模块
提供知识图谱构建、保存、加载等功能
"""
import json
import uuid
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple

from app.config import settings
from app.models import Entity, Relation, KnowledgeGraph, TextChunk
from .llm_service import LLMService
from .file_processor import FileProcessor


class KnowledgeGraphBuilder:
    """知识图谱构建器类"""
    
    def __init__(self):
        self.kg_data_dir = Path(settings.KG_DATA_DIR)
        self.kg_data_dir.mkdir(exist_ok=True)
        self.llm_service = LLMService()
        self.file_processor = FileProcessor()
        self.graphs: Dict[str, KnowledgeGraph] = {}
    
    async def build_from_text(
        self,
        text: str,
        filename: str = "",
        kg_name: str = "default",
        task_id: Optional[str] = None
    ) -> KnowledgeGraph:
        """从单个文本构建知识图谱"""
        kg_id = task_id or str(uuid.uuid4())
        
        # 分割文本为块
        chunks = self.file_processor.split_text_into_chunks(text)
        
        # 从文本块构建知识图谱
        kg = await self.build_from_chunks(chunks, kg_name, kg_id)
        
        # 更新元数据
        kg.metadata.update({
            "source_filename": filename,
            "source_text_length": len(text),
            "total_chunks": len(chunks)
        })
        
        # 保存图谱
        await self.save_knowledge_graph(kg)
        
        return kg
    
    async def build_from_chunks(
        self,
        chunks: List[TextChunk],
        kg_name: str = "default",
        kg_id: Optional[str] = None
    ) -> KnowledgeGraph:
        """从文本块构建知识图谱"""
        if not kg_id:
            kg_id = str(uuid.uuid4())
        
        all_entities = []
        all_relations = []
        
        # 提取每个文本块的实体和关系
        for i, chunk in enumerate(chunks):
            try:
                entities, relations = await self.llm_service.extract_entities_and_relations(
                    chunk.content
                )
                all_entities.extend(entities)
                all_relations.extend(relations)
            except Exception as e:
                # 如果某个块处理失败，继续处理其他块
                continue
        
        # 简单去重（基于实体名称）
        unique_entities = self._deduplicate_entities(all_entities)
        
        # 创建知识图谱
        kg = KnowledgeGraph(
            id=kg_id,
            name=kg_name,
            entities=unique_entities,
            relations=all_relations,
            metadata={
                "total_chunks": len(chunks),
                "creation_method": "chunk_extraction",
                "created_at": datetime.now().isoformat()
            }
        )
        
        return kg
    
    async def build_from_multiple_texts(
        self,
        texts: List[str],
        filenames: List[str],
        kg_name: str = "default"
    ) -> KnowledgeGraph:
        """从多个文本构建知识图谱"""
        kg_id = str(uuid.uuid4())
        
        all_entities = []
        all_relations = []
        
        # 处理每个文本
        for text, filename in zip(texts, filenames):
            if text.strip():
                try:
                    entities, relations = await self.llm_service.extract_entities_and_relations(text)
                    all_entities.extend(entities)
                    all_relations.extend(relations)
                except Exception as e:
                    # 如果某个文件处理失败，继续处理其他文件
                    continue
        
        # 去重和合并
        unique_entities = self._deduplicate_entities(all_entities)
        
        # 创建知识图谱
        kg = KnowledgeGraph(
            id=kg_id,
            name=kg_name,
            entities=unique_entities,
            relations=all_relations,
            metadata={
                "source_files": filenames,
                "total_files": len(texts),
                "creation_method": "multi_file_extraction",
                "created_at": datetime.now().isoformat()
            }
        )
        
        # 保存图谱
        await self.save_knowledge_graph(kg)
        
        return kg
    
    def _deduplicate_entities(self, entities: List[Entity]) -> List[Entity]:
        """去重实体（基于名称）"""
        unique_entities = []
        entity_names = set()
        
        for entity in entities:
            if entity.name and entity.name not in entity_names:
                unique_entities.append(entity)
                entity_names.add(entity.name)
        
        return unique_entities
    
    async def save_knowledge_graph(self, kg: KnowledgeGraph) -> bool:
        """保存知识图谱到文件"""
        try:
            file_path = self.kg_data_dir / f"{kg.id}.json"
            
            # 转换为可序列化的字典
            kg_dict = {
                "id": kg.id,
                "name": kg.name,
                "entities": [entity.model_dump() for entity in kg.entities],
                "relations": [relation.model_dump() for relation in kg.relations],
                "metadata": kg.metadata,
                "created_at": kg.created_at.isoformat(),
                "updated_at": kg.updated_at.isoformat()
            }
            
            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(kg_dict, f, ensure_ascii=False, indent=2)
            
            # 缓存到内存
            self.graphs[kg.id] = kg
            
            return True
        except Exception as e:
            return False
    
    async def load_knowledge_graph(self, kg_id: str) -> Optional[KnowledgeGraph]:
        """从文件加载知识图谱"""
        # 先检查内存缓存
        if kg_id in self.graphs:
            return self.graphs[kg_id]
        
        file_path = self.kg_data_dir / f"{kg_id}.json"
        
        if not file_path.exists():
            return None
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                kg_dict = json.load(f)
            
            # 重建实体和关系对象
            entities = [Entity(**entity_data) for entity_data in kg_dict["entities"]]
            relations = [Relation(**relation_data) for relation_data in kg_dict["relations"]]
            
            kg = KnowledgeGraph(
                id=kg_dict["id"],
                name=kg_dict["name"],
                entities=entities,
                relations=relations,
                metadata=kg_dict["metadata"],
                created_at=datetime.fromisoformat(kg_dict["created_at"]),
                updated_at=datetime.fromisoformat(kg_dict["updated_at"])
            )
            
            # 缓存到内存
            self.graphs[kg_id] = kg
            
            return kg
        except Exception as e:
            return None
    
    async def list_knowledge_graphs(self) -> List[Dict[str, Any]]:
        """列出所有知识图谱"""
        graphs = []
        
        try:
            for file_path in self.kg_data_dir.glob("*.json"):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        kg_dict = json.load(f)
                    
                    graph_info = {
                        "id": kg_dict["id"],
                        "name": kg_dict["name"],
                        "entity_count": len(kg_dict["entities"]),
                        "relation_count": len(kg_dict["relations"]),
                        "created_at": kg_dict["created_at"],
                        "updated_at": kg_dict["updated_at"],
                        "metadata": kg_dict.get("metadata", {})
                    }
                    graphs.append(graph_info)
                except Exception as e:
                    # 如果某个文件损坏，跳过它
                    continue
        except Exception as e:
            pass
        
        # 按创建时间排序
        graphs.sort(key=lambda x: x["created_at"], reverse=True)
        
        return graphs
    
    async def delete_knowledge_graph(self, kg_id: str) -> bool:
        """删除知识图谱"""
        try:
            file_path = self.kg_data_dir / f"{kg_id}.json"
            
            if file_path.exists():
                file_path.unlink()
            
            # 从内存缓存中移除
            if kg_id in self.graphs:
                del self.graphs[kg_id]
            
            return True
        except Exception as e:
            return False
    
    async def update_knowledge_graph(self, kg: KnowledgeGraph) -> bool:
        """更新知识图谱"""
        kg.updated_at = datetime.now()
        return await self.save_knowledge_graph(kg)
    
    async def merge_knowledge_graphs(
        self,
        kg_ids: List[str],
        new_kg_name: str = "merged_graph"
    ) -> Optional[KnowledgeGraph]:
        """合并多个知识图谱"""
        all_entities = []
        all_relations = []
        merged_metadata = {}
        
        # 加载所有图谱
        for kg_id in kg_ids:
            kg = await self.load_knowledge_graph(kg_id)
            if kg:
                all_entities.extend(kg.entities)
                all_relations.extend(kg.relations)
                merged_metadata[kg_id] = kg.metadata
        
        if not all_entities and not all_relations:
            return None
        
        # 去重
        unique_entities = self._deduplicate_entities(all_entities)
        
        # 创建新的合并图谱
        merged_kg = KnowledgeGraph(
            id=str(uuid.uuid4()),
            name=new_kg_name,
            entities=unique_entities,
            relations=all_relations,
            metadata={
                "merged_from": kg_ids,
                "source_metadata": merged_metadata,
                "creation_method": "merge",
                "created_at": datetime.now().isoformat()
            }
        )
        
        # 保存合并后的图谱
        await self.save_knowledge_graph(merged_kg)
        
        return merged_kg
    
    def get_graph_statistics(self, kg: KnowledgeGraph) -> Dict[str, Any]:
        """获取知识图谱统计信息"""
        entity_types = {}
        relation_types = {}
        
        # 统计实体类型
        for entity in kg.entities:
            entity_type = entity.type
            entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
        
        # 统计关系类型
        for relation in kg.relations:
            relation_type = relation.relation
            relation_types[relation_type] = relation_types.get(relation_type, 0) + 1
        
        return {
            "total_entities": len(kg.entities),
            "total_relations": len(kg.relations),
            "entity_types": entity_types,
            "relation_types": relation_types,
            "created_at": kg.created_at.isoformat(),
            "updated_at": kg.updated_at.isoformat(),
            "metadata": kg.metadata
        }
    
    async def export_knowledge_graph(
        self,
        kg_id: str,
        format: str = "json"
    ) -> Optional[str]:
        """导出知识图谱为指定格式"""
        kg = await self.load_knowledge_graph(kg_id)
        if not kg:
            return None
        
        if format.lower() == "json":
            return json.dumps({
                "id": kg.id,
                "name": kg.name,
                "entities": [entity.model_dump() for entity in kg.entities],
                "relations": [relation.model_dump() for relation in kg.relations],
                "metadata": kg.metadata,
                "created_at": kg.created_at.isoformat(),
                "updated_at": kg.updated_at.isoformat()
            }, ensure_ascii=False, indent=2)
        
        # 可以扩展支持其他格式（如CSV、GraphML等）
        return None
